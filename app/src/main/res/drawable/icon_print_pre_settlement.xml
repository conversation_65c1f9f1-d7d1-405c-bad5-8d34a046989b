<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="38dp"
    android:height="38dp"
    android:viewportWidth="38"
    android:viewportHeight="38">
  <path
      android:pathData="M19,19m-17.3,0a17.3,17.3 0,1 1,34.6 0a17.3,17.3 0,1 1,-34.6 0"
      android:strokeWidth="1.4"
      android:fillColor="#ffffff"
      android:strokeColor="#0F9D58"/>
  <path
      android:pathData="M24.667,9H13.333C13.024,9 12.869,9 12.738,9.012C11.288,9.145 10.138,10.351 10.011,11.873C10,12.01 10,12.172 10,12.497V27.26C10,28.132 11.059,28.503 11.558,27.805C11.904,27.32 12.596,27.32 12.942,27.805L13.375,28.41C13.938,29.197 15.063,29.197 15.625,28.41C16.188,27.623 17.313,27.623 17.875,28.41C18.438,29.197 19.563,29.197 20.125,28.41C20.688,27.623 21.813,27.623 22.375,28.41C22.938,29.197 24.063,29.197 24.625,28.41L25.058,27.805C25.404,27.32 26.096,27.32 26.442,27.805C26.941,28.503 28,28.132 28,27.26V12.497C28,12.172 28,12.01 27.989,11.873C27.862,10.351 26.712,9.145 25.261,9.012C25.131,9 24.976,9 24.667,9Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="27.4"
          android:startY="19"
          android:endX="9.975"
          android:endY="16.278"
          android:type="linear">
        <item android:offset="0" android:color="#FF00AF46"/>
        <item android:offset="1" android:color="#FF1ACB74"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.881,13V12.9H19.781H18.808H18.708V13V14.271C18.346,14.318 18.015,14.413 17.717,14.558C17.276,14.771 16.929,15.076 16.679,15.472L16.679,15.472L16.678,15.472C16.431,15.869 16.31,16.336 16.31,16.869C16.31,17.409 16.422,17.868 16.653,18.242L16.653,18.242L16.654,18.243C16.886,18.607 17.209,18.912 17.621,19.158L17.622,19.159C18.032,19.398 18.51,19.614 19.053,19.805L19.053,19.805C19.423,19.933 19.713,20.071 19.926,20.216L19.927,20.216C20.14,20.357 20.287,20.512 20.374,20.678L20.374,20.678L20.375,20.681C20.466,20.844 20.514,21.039 20.514,21.269C20.514,21.506 20.461,21.714 20.357,21.895L20.356,21.896C20.258,22.07 20.109,22.208 19.907,22.31C19.709,22.406 19.461,22.457 19.156,22.457C18.976,22.457 18.796,22.431 18.616,22.379C18.444,22.328 18.288,22.244 18.146,22.127C18.01,22.013 17.899,21.855 17.815,21.649C17.733,21.445 17.69,21.185 17.69,20.865V20.765H17.59H16.063H15.962V20.865C15.962,21.423 16.052,21.901 16.237,22.296L16.237,22.297C16.42,22.684 16.667,22.998 16.98,23.236C17.289,23.471 17.633,23.641 18.012,23.746L18.013,23.746C18.206,23.796 18.4,23.834 18.595,23.859V25V25.1H18.695H19.661H19.761V25V23.858C20.122,23.813 20.454,23.726 20.755,23.596L20.755,23.596C21.221,23.391 21.586,23.09 21.846,22.694C22.111,22.296 22.241,21.815 22.241,21.257C22.241,20.717 22.129,20.262 21.898,19.897C21.674,19.533 21.354,19.229 20.943,18.987C20.541,18.743 20.065,18.524 19.518,18.329C19.127,18.183 18.823,18.041 18.604,17.903C18.391,17.762 18.248,17.61 18.165,17.449C18.081,17.284 18.037,17.09 18.037,16.863C18.037,16.627 18.079,16.421 18.16,16.243C18.244,16.069 18.372,15.932 18.548,15.833L18.548,15.833L18.55,15.832C18.724,15.73 18.951,15.675 19.238,15.675C19.435,15.675 19.607,15.713 19.756,15.785L19.756,15.785L19.757,15.786C19.91,15.859 20.041,15.964 20.15,16.104L20.15,16.104L20.151,16.105C20.259,16.24 20.341,16.408 20.397,16.612L20.397,16.612L20.398,16.614C20.458,16.813 20.489,17.043 20.489,17.305V17.405H20.588H22.104H22.204V17.305C22.204,16.821 22.135,16.388 21.996,16.009C21.861,15.626 21.664,15.302 21.405,15.038C21.146,14.775 20.832,14.575 20.466,14.44C20.282,14.372 20.087,14.321 19.881,14.287V13Z"
      android:strokeWidth="0.2"
      android:fillColor="#ffffff"
      android:strokeColor="#ffffff"/>
</vector>
