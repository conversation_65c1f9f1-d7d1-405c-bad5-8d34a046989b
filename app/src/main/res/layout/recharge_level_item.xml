<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="75dp"
    android:layout_marginVertical="5dp"
    android:background="@drawable/selector_rounded_rectangle_border_12">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clRechargeLevelItem"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvRechargeAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="12dp"
            android:textColor="@color/black80"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="$100.00" />

        <TextView
            android:id="@+id/tvGiftAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:textColor="@color/primaryColor"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvRechargeAmount"
            tools:text="赠$20.00" />

        <View
            android:id="@+id/vLine"
            android:layout_width="1dp"
            android:layout_height="12sp"
            android:layout_marginStart="6dp"
            android:background="@color/black12"
            app:layout_constraintBottom_toBottomOf="@+id/tvGiftAmount"
            app:layout_constraintStart_toEndOf="@+id/tvGiftAmount"
            app:layout_constraintTop_toTopOf="@+id/tvGiftAmount" />


        <TextView
            android:id="@+id/tvGiftCoupon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="12dp"
            android:text="@string/give_coupons"
            android:textColor="@color/color_ff7f00"
            android:textSize="12sp"
            app:drawableEndCompat="@drawable/ic_arrow_right_yellow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/vLine"
            app:layout_constraintTop_toBottomOf="@+id/tvRechargeAmount"
            app:layout_goneMarginStart="20dp" />

        <TextView
            android:id="@+id/tvEditRecharge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="10dp"
            android:layout_marginVertical="6dp"
            android:drawablePadding="4dp"
            android:padding="6dp"
            android:text="@string/edit"
            android:textColor="@color/primaryColor"
            android:textSize="12sp"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_edit_recharger"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvCustomRecharge"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="20dp"
        android:gravity="center_vertical"
        android:text="@string/custom_recharge"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/tvSelectMark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:importantForAccessibility="no"
        android:src="@drawable/ic_item_select_mark"
        android:layout_gravity="end"
        android:visibility="gone"
        tools:visibility="visible" />

</FrameLayout>
