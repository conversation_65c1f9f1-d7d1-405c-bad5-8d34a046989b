<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <com.metathought.food_order.casheir.ui.widget.keyboard.KeyboardView
        android:id="@+id/keyboard_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="3dp"
        app:keyBackground="@drawable/selector_blue_keyboard"
        app:keyTextColor="@color/black70"
        app:keyTextSize="30sp"
        app:labelTextSize="@dimen/_18ssp"
        app:shadowColor="@color/transparent" />


    <!--    <ProgressBar android:id="@+id/progress"-->
    <!--        android:layout_width="30dp"-->
    <!--        android:layout_height="30dp"-->
    <!--        android:layout_gravity="center"-->
    <!--        android:indeterminate="true"-->
    <!--        android:indeterminateTint="@color/basic_theme_colors"/>-->
</merge>