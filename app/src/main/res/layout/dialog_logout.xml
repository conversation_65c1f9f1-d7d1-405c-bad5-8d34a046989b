<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="400dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/log_out"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/layoutWrap"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/background_dialog_info"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvTitle"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/staff_info"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/staff_name"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvStaffName"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="@tools:sample/lorem/random" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/login_time"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvLoginTime"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="2024/01/11  12:05:02" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_orders"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvTotalOrder"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="@tools:sample/lorem/random" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/FontLocalization"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/unpaid_orders"
                            android:textColor="@color/black"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvUnpaidOrder"
                            style="@style/FontLocalization"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:gravity="end"
                            android:maxLines="2"
                            android:textColor="@color/black"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="@tools:sample/lorem/random" />
                    </LinearLayout>
                </LinearLayout>

<!--                <TextView-->
<!--                    style="@style/FontLocalization"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginVertical="16dp"-->
<!--                    android:maxLines="2"-->
<!--                    android:text="@string/amount_of_cash_handed_over"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="16sp" />-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:gravity="center_vertical"-->
<!--                    android:orientation="horizontal">-->

<!--                    <com.google.android.material.textfield.TextInputLayout-->
<!--                        style="@style/CustomOutlinedBox"-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_marginHorizontal="10dp"-->
<!--                        android:layout_weight="1"-->
<!--                        android:hint="@string/amount_require"-->
<!--                        android:textColorHint="@color/bg_progress">-->

<!--                        <com.google.android.material.textfield.TextInputEditText-->
<!--                            android:id="@+id/edtUSD"-->
<!--                            style="@style/FontLocalization"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="match_parent"-->
<!--                            android:drawableStart="@drawable/ic_dollar"-->
<!--                            android:drawablePadding="10dp"-->
<!--                            android:drawableTint="@color/black"-->
<!--                            android:inputType="numberDecimal"-->
<!--                            android:maxLines="1"-->
<!--                            android:singleLine="true"-->
<!--                            android:textColor="@color/black" />-->
<!--                    </com.google.android.material.textfield.TextInputLayout>-->

<!--                    <com.google.android.material.textfield.TextInputLayout-->
<!--                        style="@style/CustomOutlinedBox"-->
<!--                        android:layout_width="0dp"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_weight="1"-->
<!--                        android:hint="@string/amount_require"-->
<!--                        android:textColorHint="@color/bg_progress">-->

<!--                        <com.google.android.material.textfield.TextInputEditText-->
<!--                            android:id="@+id/edtKhmer"-->
<!--                            style="@style/FontLocalization"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="match_parent"-->
<!--                            android:digits="0123456789"-->
<!--                            android:drawableStart="@drawable/icon_discount_khr"-->
<!--                            android:drawablePadding="10dp"-->
<!--                            android:inputType="numberDecimal"-->
<!--                            android:maxLines="1"-->
<!--                            android:singleLine="true"-->
<!--                            android:textColor="@color/black" />-->
<!--                    </com.google.android.material.textfield.TextInputLayout>-->


<!--                </LinearLayout>-->

                <LinearLayout
                    android:id="@+id/btnLogout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_marginTop="30dp"
                    android:background="@drawable/button_login_background"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent">

                    <TextView
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/log_out"
                        android:textColor="@color/mainWhite"
                        android:textSize="18sp" />

                    <ProgressBar
                        android:id="@+id/pbConfirm"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginStart="10dp"
                        android:indeterminate="true"
                        android:indeterminateTint="@color/mainWhite"
                        android:indeterminateTintMode="src_atop"
                        android:progressTint="@color/mainWhite"
                        android:visibility="gone" />
                </LinearLayout>
            </LinearLayout>

            <ProgressBar
                android:id="@+id/pbLogout"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:visibility="gone"
                tools:visibility="visible" />
        </FrameLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
