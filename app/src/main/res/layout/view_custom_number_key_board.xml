<?xml version="1.0" encoding="utf-8"?>
<GridLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="3"
    android:rowCount="4">

    <TextView
        android:id="@+id/tvNumber1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="1"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="2"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_topright_radius_16dp"
        android:gravity="center"
        android:text="3"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber4"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="4"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber5"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="5"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber6"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="6"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber7"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="7"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber8"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="8"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber9"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="9"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvPoint"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="."
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvNumber0"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="0"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />


    <FrameLayout
        android:id="@+id/tvDelete"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_bottomright_radius_16dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/icon_keyboard_delete" />
    </FrameLayout>

    <TextView
        android:id="@+id/tvClear"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_white_radius_4dp"
        android:gravity="center"
        android:text="@string/clear_cart"
        android:textColor="@color/black"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvConfirm"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_rowWeight="1"
        android:layout_columnSpan="2"
        android:layout_columnWeight="1"
        android:layout_margin="2dp"
        android:background="@drawable/background_primary_bottom_right_radius_16dp"
        android:gravity="center"
        android:text="@string/confirm2"
        android:textColor="@color/white"
        android:textSize="28sp"
        android:textStyle="bold" />
</GridLayout>