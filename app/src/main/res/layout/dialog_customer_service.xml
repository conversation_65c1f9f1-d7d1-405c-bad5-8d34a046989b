<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        app:cardCornerRadius="16dp"
        app:cardElevation="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginVertical="16dp">

            <TextView
                android:id="@+id/tvPhone"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="14dp"
                android:text="@string/service_phone"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold"
                app:drawableStartCompat="@drawable/icon_service_phone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/ivQrCode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:src="@mipmap/icon_mos_service_qrcode"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvPhone" />

            <TextView
                android:id="@+id/tvCue"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:drawablePadding="14dp"
                android:gravity="center"
                android:text="@string/contact_us_cue"
                android:textColor="@color/black40"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivQrCode" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnContact"
                style="@style/CustomOutlinedBox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="16dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="horizontal"
                android:text="@string/contact_now"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="@dimen/_14ssp"
                app:backgroundTint="@color/primaryColor"
                app:cornerRadius="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvCue" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>
