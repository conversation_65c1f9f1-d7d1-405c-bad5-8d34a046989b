<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="520dp"
        android:layout_height="wrap_content"
        android:background="@drawable/background_dialog"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        android:paddingHorizontal="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <com.metathought.food_order.casheir.ui.widget.DialogTopBar
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="24dp"
            app:dialog_title="@string/add_new_tmp_good" />

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutName"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/product_name_required"
            android:textColorHint="@color/black60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtName"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="10dp"
                android:inputType="text"
                android:maxLength="200"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutPrice"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/product_sale_price"
            android:textColorHint="@color/black60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtPrice"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="10dp"
                android:inputType="numberDecimal"
                android:maxLength="9"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutRemark"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="140dp"
            android:layout_marginBottom="16dp"
            android:hint="@string/remark"
            android:textColorHint="@color/black60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtRemark"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="10dp"
                android:gravity="top|start"
                android:inputType="textMultiLine"
                android:maxLength="1000"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>


        <TextView
            android:id="@+id/btnYes"
            style="@style/CustomOutlinedBox"
            android:layout_width="370dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:layout_marginVertical="24dp"
            android:alpha="0.5"
            android:background="@drawable/background_primary_color_radius_12dp"
            android:drawablePadding="10dp"
            android:enabled="false"
            android:gravity="center"
            android:paddingHorizontal="19dp"
            android:text="@string/done"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="@dimen/_18ssp"
            android:textStyle="bold" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
