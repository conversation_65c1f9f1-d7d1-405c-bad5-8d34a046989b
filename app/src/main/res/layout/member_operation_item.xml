<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="150dp"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:layout_gravity="center_vertical"
    android:paddingStart="10dp"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvEdit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/edit"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_14ssp" />

    <TextView
        android:id="@+id/tvDetail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/detail"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_14ssp" />

    <TextView
        android:id="@+id/tvTopUp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/top_up"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_14ssp" />

</LinearLayout>
