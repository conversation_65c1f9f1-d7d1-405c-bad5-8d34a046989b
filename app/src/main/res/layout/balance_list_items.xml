<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:orientation="horizontal">

    <!-- 中间可滑动部分 -->
    <HorizontalScrollView
        android:id="@+id/balanceItemScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadeScrollbars="false"
        android:overScrollMode="never"
        android:scrollbarStyle="outsideOverlay"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvAccountNumber"
                style="@style/FontLocalization"
                android:layout_width="200dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="***********" />

            <TextView
                android:id="@+id/tvRechargeAmount"
                style="@style/FontLocalization"
                android:layout_width="200dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="$100" />

            <TextView
                android:id="@+id/tvType"
                style="@style/FontLocalization"
                android:layout_width="150dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="Paid" />

            <TextView
                android:id="@+id/tvPaymentMethod"
                style="@style/FontLocalization"
                android:layout_width="150dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="支付方式" />

            <LinearLayout
                android:layout_width="150dp"
                android:layout_height="match_parent"
                android:paddingEnd="10dp">

                <ImageView
                    android:id="@+id/imgStatus"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="5dp"
                    android:src="@drawable/ic_circle" />

                <TextView
                    android:id="@+id/tvStatus"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="2"
                    android:paddingEnd="10dp"
                    android:singleLine="true"
                    android:text="@string/top_up"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    tools:text="Top Up" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvLastTopUpDate"
                style="@style/FontLocalization"
                android:layout_width="200dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="2024/02/02  12:00:00" />

        </LinearLayout>
    </HorizontalScrollView>

</LinearLayout>