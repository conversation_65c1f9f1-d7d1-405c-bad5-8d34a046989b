<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <View
        style="@style/commonDividerStyle"
        android:layout_marginVertical="10dp" />

    <LinearLayout
        android:id="@+id/layoutContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:layout_weight="1"
                android:text="@string/gift_product"
                android:textColor="@color/primaryColor"
                android:textSize="@dimen/_12ssp" />

            <TextView
                android:id="@+id/tvCount"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="6dp"
                android:textColor="@color/black"
                android:textSize="@dimen/_12ssp"
                tools:text="共2件" />

            <ImageView
                android:id="@+id/ivArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_arrow_down" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvGiftActivityTag"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/background_border_primary_4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingHorizontal="4dp"
            android:paddingVertical="3dp"
            android:text="满$100有赠品"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_11ssp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_max="150dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:itemCount="5"
                tools:listitem="@layout/pending_order_menu_item" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>


</LinearLayout>