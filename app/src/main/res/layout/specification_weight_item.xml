<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="10dp"
    android:background="@android:color/transparent"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tvSinglePrice"
        style="@style/FontLocalization"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/_12ssp"
        tools:text="单价：$99.99/KG" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="5dp"
        android:layout_marginTop="10dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutWeight"
            style="@style/CustomOutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/please_input_product_weight"
            android:textColorHint="@color/black60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edtWeight"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:drawablePadding="10dp"
                android:inputType="numberDecimal"
                android:maxLength="60"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textColorHint="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>


        <TextView
            android:id="@+id/tvUnit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="14dp"
            android:textSize="@dimen/_16ssp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="KG" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/llWeightSeting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="@string/enable_manual_input"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/ivSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:src="@drawable/icon_switch_close" />
    </LinearLayout>

</LinearLayout>
