<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="60dp"
    android:orientation="horizontal">

    <!-- 左侧内容区域 - 与表头同步滑动 -->
    <HorizontalScrollView
        android:id="@+id/itemScrollView"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:scrollbars="none"
        android:paddingStart="0dp"
        android:paddingEnd="0dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingStart="0dp"
            android:paddingEnd="0dp">

            <TextView
                android:id="@+id/tvAccountName"
                style="@style/FontLocalization"
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="林超正超正常的..." />

            <TextView
                android:id="@+id/tvAccountNumber"
                style="@style/FontLocalization"
                android:layout_width="140dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="***********" />

            <TextView
                android:id="@+id/tvAmount"
                style="@style/FontLocalization"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="$ 9.9" />

            <TextView
                android:id="@+id/tvRegisterDate"
                style="@style/FontLocalization"
                android:layout_width="160dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="2024/02/02  12:00:00" />

            <TextView
                android:id="@+id/tvLastTopUpDate"
                style="@style/FontLocalization"
                android:layout_width="160dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="2024/02/02  12:00:00" />

            <TextView
                android:id="@+id/tvRechargeTimes"
                style="@style/FontLocalization"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="1000" />

            <TextView
                android:id="@+id/tvConsumptionTimes"
                style="@style/FontLocalization"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:paddingStart="0dp"
                android:paddingEnd="10dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                tools:text="1000" />

        </LinearLayout>
    </HorizontalScrollView>

    <!-- 右侧操作按钮 - 固定显示 -->
    <LinearLayout
        android:layout_width="150dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:paddingStart="10dp">

        <TextView
            android:id="@+id/tvEdit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/edit"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_14ssp" />

        <TextView
            android:id="@+id/tvDetail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/detail"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_14ssp" />

        <TextView
            android:id="@+id/tvTopUp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/top_up"
            android:textColor="@color/primaryColor"
            android:textSize="@dimen/_14ssp" />
    </LinearLayout>

</LinearLayout>