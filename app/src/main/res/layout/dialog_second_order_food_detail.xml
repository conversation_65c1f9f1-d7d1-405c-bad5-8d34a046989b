<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/background_dialog"
    android:minWidth="600dp"
    android:orientation="vertical"
    android:paddingHorizontal="10dp"
    android:paddingVertical="10dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:layout_marginBottom="5dp"
        android:gravity="end"
        android:paddingHorizontal="16dp"
        tools:ignore="UseCompoundDrawables">

        <TextView
            android:id="@+id/tvDishedName"
            style="@style/FontLocalization"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="start"
            android:text="@string/item_detail"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="Item Detail" />

        <ImageView
            android:id="@+id/btnClose"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:padding="5dp"
            android:src="@drawable/ic_cross_closed"
            tools:ignore="ContentDescription" />
    </LinearLayout>


    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:orientation="horizontal">

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="0dp">

                    <com.denzcoskun.imageslider.ImageSlider
                        android:id="@+id/imageSlider"
                        android:layout_width="200dp"
                        android:layout_height="100dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_food"
                        app:iss_auto_cycle="true"
                        app:iss_delay="5000"
                        app:iss_error_image="@drawable/icon_menu_default2"
                        app:iss_period="5000"
                        app:iss_placeholder="@drawable/icon_menu_default2"
                        app:iss_text_align="CENTER" />
                </androidx.cardview.widget.CardView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDetailInfo"
                        style="@style/FontLocalization"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="15dp"
                        android:textColor="@color/black"
                        android:textSize="14sp"
                        app:layout_constrainedHeight="true"
                        app:layout_constraintBottom_toTopOf="@id/llDesc"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="@tools:sample/lorem/random" />

                    <LinearLayout
                        android:id="@+id/llDesc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="15dp"
                        android:layout_marginTop="6dp"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toTopOf="@id/tvServiceChargePercentage"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvDetailInfo"
                        app:layout_constraintVertical_chainStyle="packed">

                        <TextView
                            android:id="@+id/tvDiscountRate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="6dp"
                            android:background="@drawable/background_e7f5ee_radius_4"
                            android:padding="2dp"
                            android:textColor="@color/primaryColor"
                            android:textSize="@dimen/_11ssp"
                            android:visibility="gone"
                            tools:text="70% OFF"
                            tools:visibility="visible" />

                        <androidx.cardview.widget.CardView
                            android:id="@+id/cdDiscountActivity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:cardBackgroundColor="@color/transparent"
                            app:cardCornerRadius="4dp"
                            app:cardElevation="0dp"
                            app:strokeColor="@color/primaryColor"
                            app:strokeWidth="1dp">

                            <TextView
                                android:id="@+id/tvDiscountActivity"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="2dp"
                                android:textColor="@color/primaryColor"
                                android:textSize="@dimen/_11ssp"
                                android:visibility="gone"
                                tools:text="70% OFF"
                                tools:visibility="visible" />
                        </androidx.cardview.widget.CardView>


                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvServiceChargePercentage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:paddingHorizontal="15dp"
                        android:textColor="@color/primaryColor"
                        android:textSize="@dimen/_12ssp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/llDesc"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="需收取4%服务费" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvMealSet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/meal_set_group_item" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerSpecification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/second_specification_main_item" />

            <TextView
                android:id="@+id/tvToppingsTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="5dp"
                android:text="@string/toppings"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerTopping"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                android:scrollbars="none"
                tools:itemCount="6"
                tools:listitem="@layout/second_topping_item" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</LinearLayout>

