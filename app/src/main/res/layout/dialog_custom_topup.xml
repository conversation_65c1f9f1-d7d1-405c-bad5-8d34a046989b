<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:id="@+id/layoutMain"
        android:layout_width="400dp"
        android:layout_height="match_parent"
        android:background="@drawable/background_dialog"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/layoutTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="UseCompoundDrawables">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/custom_recharge"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/btnClose"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:padding="5dp"
                android:src="@drawable/ic_cross_closed"
                tools:ignore="ContentDescription" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">


            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutAmount"
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/top_up_amount_require"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtTopUpAmount"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableStart="@drawable/ic_dollar"
                    android:drawablePadding="10dp"
                    android:drawableTint="@color/black80"
                    android:imeOptions="actionDone"
                    android:inputType="numberDecimal"
                    android:maxLength="8"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                style="@style/CustomOutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:hint="@string/additional_gift_amount"
                android:textColorHint="@color/black60">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edtExtraBonus"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:drawableStart="@drawable/ic_dollar"
                    android:drawablePadding="10dp"
                    android:drawableTint="@color/black80"
                    android:imeOptions="actionDone"
                    android:inputType="numberDecimal"
                    android:maxLength="12"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:textColorHint="@color/black" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/extra_coupon_giveaway"
                android:textColor="@color/black"
                android:textSize="16sp" />

            <include
                android:id="@+id/layoutEmpty"
                layout="@layout/layout_empty_data"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="8dp"
                android:layout_weight="1">

                <com.scwang.smart.refresh.header.MaterialHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvRechargeTierPage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="10"
                    tools:listitem="@layout/item_dialog_gift_coupon_detail_list" />

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

            <LinearLayout
                android:id="@+id/btnConfirm"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:alpha="0.5"
                android:background="@drawable/button_login_background"
                android:clickable="false"
                android:enabled="false"
                android:focusable="false"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/confirm2"
                    android:textColor="@color/mainWhite"
                    android:textSize="18sp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
