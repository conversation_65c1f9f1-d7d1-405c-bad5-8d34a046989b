<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:orientation="horizontal">

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="0.4">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:src="@drawable/ic_logo"
            android:text="@string/profile"
            android:textColor="@color/black80"
            android:textSize="16sp" />
    </FrameLayout>

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="林超正超正常的..."
        android:textColor="@color/black"
        android:textSize="16sp" />

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="12345678901"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.7"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="$ 9.9"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="2024/02/02  12:00:00"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="2024/02/02  12:00:00"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <TextView
        style="@style/FontLocalization"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.7"
        android:gravity="center_vertical"
        android:paddingHorizontal="10dp"
        android:text="Top Up"
        android:textColor="@color/primaryColor"
        android:textSize="16sp" />
</LinearLayout>