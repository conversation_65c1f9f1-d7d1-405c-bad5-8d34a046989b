<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_white_radius_12dp"
    android:orientation="vertical"
    android:paddingHorizontal="10dp"
    android:paddingBottom="10dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llCustomerTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvCustomerTitle"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/customer_info"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_18ssp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/ivEditCustomer"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingStart="50dp"
                    android:text="@string/edit"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPeople"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="15dp"
                    android:text="@string/print_title_number_of_pax"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp" />

                <TextView
                    android:id="@+id/tvCustomerNum"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="5" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCustomerName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/customer_name"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvCustomerName"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="林超正" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCustomerPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/phone_number"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_12ssp" />

                <TextView
                    android:id="@+id/tvCustomerPhone"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="15006099788" />
            </LinearLayout>


            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/order_info"
                android:textColor="@color/black"
                android:textSize="@dimen/_18ssp"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/llOrderStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/status"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderStatus"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTakeOutPlatform"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/take_out_platform"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvTakeOutPlatform"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llTakeOutId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/take_out_order_id"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvTakeOutId"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />

                <ImageView
                    android:id="@+id/ivEditTakeOutId"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="10dp"
                    android:src="@drawable/icon_edit"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llOrderTable"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/table"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderTable"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPickUpNo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/print_title_pick_up_no"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPickUpNo"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/order_id"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderNo"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:id="@id/llInvoiceNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/print_title_invoiceNumber"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvInvoiceNumber"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="20241234567890" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/ordered_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/order_type"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderType"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="Dine In" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/order_by"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvOrderBy"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="Kiosk" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaymentMethod1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/paymentMethod1"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/payment_method"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPaymentMethod1"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="U-Pay" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaymentMethodPayAmount1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/paymentMethodPayAmount1"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/pay_amount"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPaymentMethodPayAmount1"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="U-Pay" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaymentMethod2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/paymentMethod2"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/payment_method"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPaymentMethod2"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="U-Pay" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llPaymentMethodPayAmount2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/paymentMethodPayAmount2"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/pay_amount"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPaymentMethodPayAmount2"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="U-Pay" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llChange"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/change"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/back_your_change_amount"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvChange"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="U-Pay" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llDiningTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/ordered_dining_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvDiningTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCancelTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/cancel_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvCancelTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCancelReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/cancel_reason"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />


                <com.metathought.food_order.casheir.ui.widget.ExpandTextView
                    android:id="@+id/tvCancelReason"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="visible" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llPaymentTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/payment_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvPaymentTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRefundTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/refund_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvRefundTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRefundPayType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/refund_pay_type"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvRefundPayType"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:lineSpacingExtra="10dp"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/remark"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvOrderRemark"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="多加点醋，不吃辣，葱多放点可以吗？动换行" />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </LinearLayout>

            <LinearLayout
                android:id="@+id/llAntiSettlementReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/anti_settlement_reason"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvOrderAntiSettlement"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="多加点醋，不吃辣，葱多放点可以吗？动换行" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llUserAccount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/customer_account"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvUserAccount"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:lineSpacingExtra="10dp"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llUserName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/customer_nickname"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvUserName"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="2024/04/02 12:00:00" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llCreditReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/credit_reason"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvCreditReason"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="2024/04/02 12:00:00" />
                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llCancelCreditReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/cancel_credit"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/tvCancelCreditReason"
                        style="@style/FontLocalization"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_14ssp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="2024/04/02 12:00:00" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llRepaymentTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:text="@string/repayment_time"
                    android:textColor="@color/black60"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    android:id="@+id/tvRepaymentTime"
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:lineSpacingExtra="10dp"
                    android:maxLines="2"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="2024/04/02 12:00:00" />
            </LinearLayout>
        </LinearLayout>

    </ScrollView>


    <View
        style="@style/commonDividerStyle"
        android:layout_marginVertical="17dp" />

    <LinearLayout
        android:id="@+id/layoutTotal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/subtotal"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvSubtotal"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llDiscountActivity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvDiscountActivityTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/discount_activity"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnDiscountActivityPriceCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvDiscountActivityAmount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPackPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/packing_price"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnPackPriceCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn" />

            <TextView
                android:id="@+id/tvPackingAmount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llServiceFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="top"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/service_fee"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnServiceFeeCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvServiceFee"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="$99.99" />

                <!--                            <TextView-->
                <!--                                android:id="@+id/tvVipServiceFee"-->
                <!--                                style="@style/FontLocalization"-->
                <!--                                android:layout_width="wrap_content"-->
                <!--                                android:layout_height="wrap_content"-->
                <!--                                android:layout_marginStart="2dp"-->
                <!--                                android:layout_marginTop="4dp"-->
                <!--                                android:drawablePadding="2dp"-->
                <!--                                android:textColor="@color/member_price_color"-->
                <!--                                android:textSize="@dimen/_14ssp"-->
                <!--                                android:textStyle="bold"-->
                <!--                                android:visibility="gone"-->
                <!--                                app:drawableStartCompat="@drawable/icon_vip"-->
                <!--                                tools:text="$0.00"-->
                <!--                                tools:visibility="visible" />-->

            </LinearLayout>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/llCoupon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/coupon"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <LinearLayout
                android:id="@+id/llCouponContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end">

                <!--                            <TextView-->
                <!--                                android:id="@+id/tvVipCoupon"-->
                <!--                                style="@style/FontLocalization"-->
                <!--                                android:layout_width="wrap_content"-->
                <!--                                android:layout_height="wrap_content"-->
                <!--                                android:drawableStart="@drawable/icon_vip"-->
                <!--                                android:drawablePadding="2dp"-->
                <!--                                android:textColor="@color/member_price_color"-->
                <!--                                android:textSize="14sp"-->
                <!--                                android:textStyle="bold"-->
                <!--                                android:visibility="gone"-->
                <!--                                tools:text="$0.00"-->
                <!--                                tools:visibility="visible" />-->

                <TextView
                    android:id="@+id/tvViewCouponGiftGood"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:gravity="end"
                    android:maxLines="1"
                    android:text="@string/view_give_away_goods"
                    android:textColor="@color/primaryColor"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvCoupon"
                    style="@style/FontLocalization"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    android:visibility="visible"
                    tools:text="-$99.99"
                    tools:visibility="visible" />

                <!--                <ImageView-->
                <!--                    android:id="@+id/iconCouponArrow"-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="match_parent"-->
                <!--                    android:layout_marginStart="5dp"-->
                <!--                    android:paddingHorizontal="5dp"-->
                <!--                    android:src="@drawable/icon_coupon_arrow_right" />-->
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llDiscount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvDiscountTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/discounts2"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnDiscountCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvDiscount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:drawablePadding="4dp"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llDiscountAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <!-- 折扣减免金额的 -->
            <TextView
                android:id="@+id/tvDiscountAmountTitle"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/discounts2"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnDiscountAmountCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn"
                android:visibility="gone"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvDiscountAmount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:drawablePadding="4dp"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llVat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/titleVat"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/vat"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvVat"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llCommission"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="4dp"
                android:gravity="center_vertical"
                android:text="@string/commission"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <ImageView
                android:id="@+id/btnCommissionCue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:src="@drawable/icon_circle_warn"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvCommissionPrice"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llTotalPrice2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="top"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="top"
                android:text="@string/total"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTotalPrice2"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="$99.99" />

                <TextView
                    android:id="@+id/tvTotalKhrPrice2"
                    style="@style/FontLocalization"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp"
                    android:textStyle="bold"
                    tools:text="$99.99" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPartialRefundAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/partial_refund_amount"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvPartialRefundAmount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/main_red"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="-$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPartialRefundPackFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/refund_pack_fee"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvPartialRefundPackFee"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/main_red"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="-$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llPartialRefundServiceFee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/refund_service_fee"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvPartialRefundServiceFee"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/main_red"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="-$99.99" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llVatRefundAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="9dp"
            android:gravity="center_vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="15dp"
                android:gravity="center_vertical"
                android:text="@string/vat_refund"
                android:textColor="@color/black60"
                android:textSize="@dimen/_14ssp" />

            <TextView
                android:id="@+id/tvVatRefundAmount"
                style="@style/FontLocalization"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/main_red"
                android:textSize="@dimen/_14ssp"
                android:textStyle="bold"
                tools:text="-$99.99" />
        </LinearLayout>

        <!--                    <LinearLayout-->
        <!--                        android:layout_width="match_parent"-->
        <!--                        android:layout_height="wrap_content"-->
        <!--                        android:layout_marginTop="5dp"-->
        <!--                        android:gravity="center_vertical">-->


        <!--                    <LinearLayout-->
        <!--                        android:layout_width="match_parent"-->
        <!--                        android:layout_height="wrap_content"-->
        <!--                        android:layout_marginTop="5dp"-->
        <!--                        android:gravity="center_vertical">-->

        <!--                        <TextView-->
        <!--                            style="@style/FontLocalization"-->
        <!--                            android:layout_width="wrap_content"-->
        <!--                            android:layout_height="match_parent"-->
        <!--                            android:layout_marginEnd="15dp"-->
        <!--                            android:gravity="center_vertical"-->
        <!--                            android:text="@string/discounted"-->
        <!--                            android:textColor="@color/black80"-->
        <!--                            android:textSize="@dimen/_12ssp" />-->

        <!--                        <TextView-->
        <!--                            style="@style/FontLocalization"-->
        <!--                            android:layout_width="0dp"-->
        <!--                            android:layout_height="wrap_content"-->
        <!--                            android:layout_weight="1"-->
        <!--                            android:gravity="end"-->
        <!--                            android:maxLines="1"-->
        <!--                            android:textColor="@color/black80"-->
        <!--                            android:textSize="@dimen/_12ssp"-->
        <!--                            android:textStyle="bold"-->
        <!--                            tools:text="-$99.99" />-->
        <!--                    </LinearLayout>-->


    </LinearLayout>


    <LinearLayout
        android:id="@+id/llTotalPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:gravity="center_vertical"
        tools:visibility="visible">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="6dp"
            android:text="@string/total_price"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <ImageView
            android:id="@+id/btnTotalPriceDetail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="15dp"
            android:src="@drawable/icon_circle_warn"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTotalPrice"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="28sp"
                android:textStyle="bold"
                tools:text="$0" />

            <TextView
                android:id="@+id/tvTotalKhrPrice"
                style="@style/FontLocalization"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="28sp"
                android:textStyle="bold"
                tools:text="៛0" />

            <TextView
                android:id="@+id/tvVipPrice"
                style="@style/FontLocalization"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:drawablePadding="2dp"
                android:textColor="@color/member_price_color"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:drawableStartCompat="@drawable/icon_vip"
                tools:text="$0.00"
                tools:visibility="visible" />

            <!--            <TextView-->
            <!--                android:id="@+id/tvOriginalPrice"-->
            <!--                style="@style/FontLocalization"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_marginTop="4dp"-->
            <!--                android:drawablePadding="2dp"-->
            <!--                android:foreground="@drawable/strike_price"-->
            <!--                android:textColor="@color/black60"-->
            <!--                android:textSize="16sp"-->
            <!--                android:textStyle="bold"-->
            <!--                android:visibility="gone"-->
            <!--                tools:text="$0.00"-->
            <!--                tools:visibility="visible" />-->


        </LinearLayout>

        <!--                    <TextView-->
        <!--                        android:id="@+id/tvTotalPrice"-->
        <!--                        style="@style/FontLocalization"-->
        <!--                        android:layout_width="0dp"-->
        <!--                        android:layout_height="wrap_content"-->
        <!--                        android:layout_weight="1"-->
        <!--                        android:gravity="end"-->
        <!--                        android:maxLines="1"-->
        <!--                        android:textColor="@color/black"-->
        <!--                        android:textSize="20sp"-->
        <!--                        android:textStyle="bold"-->
        <!--                        tools:text="$99.99" />-->
    </LinearLayout>


    <LinearLayout
        android:id="@+id/llActualReceiveAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/actual_received_amount"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/tvActualReceiveAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="$99.99" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llRefundAmount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="15dp"
            android:gravity="center_vertical"
            android:text="@string/refund_amount"
            android:textColor="@color/black"
            android:textSize="@dimen/_16ssp" />

        <TextView
            android:id="@+id/tvRefundAmount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            tools:text="$99.99" />
    </LinearLayout>
</LinearLayout>