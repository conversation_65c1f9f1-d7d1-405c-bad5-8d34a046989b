<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        style="@style/commonDividerStyle"
        android:layout_height="1dp"
        android:background="@color/black" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="4px">

        <TextView
            android:id="@+id/tvFoodName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:ellipsize="end"
            android:textColor="@color/black"
            android:textSize="@dimen/_printer_default_sp"
            tools:text="Pasta" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/llItemIndex"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.6"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvIndex"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="1" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFoodName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_weight="1.2"
                android:orientation="vertical"
                android:textColor="@color/black50"
                android:textSize="14sp"
                android:visibility="invisible"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvFoodNameEn"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:ellipsize="end"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="Pasta" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFoodCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvFoodCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="center_horizontal"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="x1" />

                <!--            <TextView-->
                <!--                android:id="@+id/tvRefundMinusCount"-->
                <!--                android:layout_width="match_parent"-->
                <!--                android:layout_height="wrap_content"-->
                <!--                android:layout_gravity="end"-->
                <!--                android:layout_marginTop="5dp"-->
                <!--                android:gravity="center_horizontal"-->
                <!--                tools:text="1"-->
                <!--                android:visibility="gone"-->
                <!--                android:textColor="@color/main_red"-->
                <!--                android:textSize="@di" />-->
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFoodPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1.4"
                android:gravity="center"
                android:orientation="vertical"
                android:text="@string/items"
                android:textColor="@color/black50"
                android:textSize="14sp">

                <TextView
                    android:id="@+id/tvFoodPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="$9.9" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFoodDiscountPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1.4"
                android:gravity="end"
                android:orientation="vertical"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvFoodDiscountPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="$0.00"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/llFoodTotalPrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_weight="1.4"
                android:gravity="center"
                android:orientation="vertical"
                android:text="@string/items"
                android:textColor="@color/black50"
                android:textSize="14sp">

                <TextView
                    android:id="@+id/tvFoodTotalPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_printer_default_sp"
                    tools:text="$9.9" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvFoodNameKh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:ellipsize="end"
            android:textColor="@color/black"
            android:textSize="@dimen/_printer_default_sp"
            android:visibility="gone"
            tools:text="ប៉ាស្តា"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/setMealRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_printer_feed" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/specRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_printer_feed" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/feedRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_printer_feed" />

    </LinearLayout>
</LinearLayout>
