<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    app:cardCornerRadius="20dp"
    app:cardElevation="0dp"
    app:cardBackgroundColor="@android:color/transparent"
    app:strokeColor="@android:color/transparent">

    <TextView
        android:backgroundTint="@color/mainWhite"
        android:id="@+id/tvValue"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingHorizontal="25dp"
        android:text="Floor1"
        style="@style/FontLocalization"
        android:gravity="center"
        android:textSize="14sp"
        android:textColor="@color/black"
        android:textStyle="bold" />
</com.google.android.material.card.MaterialCardView>