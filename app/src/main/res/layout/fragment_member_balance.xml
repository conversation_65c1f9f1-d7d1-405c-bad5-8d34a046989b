<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_table_fragment"
    tools:context=".ui.table.TableFragment">

    <RadioGroup
        android:id="@+id/radioGroupFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/round_background_25dp"
        android:checkedButton="@id/radioAll"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioOverview"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/overview"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioMember"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/customer"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioBalance"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/details"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

        <RadioButton
            android:id="@+id/radioCredit"
            style="@style/FontLocalization"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/radiobutton_filter_backgroud"
            android:button="@null"
            android:gravity="center"
            android:minHeight="0dp"
            android:paddingHorizontal="15dp"
            android:text="@string/credit"
            android:textColor="@drawable/radio_filter_text_selector"
            android:textSize="@dimen/_14ssp"
            app:buttonCompat="@null" />

    </RadioGroup>

    <LinearLayout
        android:id="@+id/dropdownFilter"
        android:layout_width="150dp"
        android:layout_height="32dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/background_language_spiner"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10dp"
        android:visibility="visible"
        app:layout_constraintStart_toEndOf="@id/radioGroupFilter"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvType"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/all"
            android:textColor="@color/black"
            android:textSize="@dimen/_14ssp"
            tools:text="@string/all" />

        <ImageView
            android:id="@+id/arrow"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_dropdown"
            android:visibility="visible"
            tools:ignore="ContentDescription" />
    </LinearLayout>


    <!--    <EditText-->
    <!--        android:id="@+id/edtSearch"-->
    <!--        style="@style/commonSearchStyle"-->
    <!--        android:layout_marginEnd="6dp"-->
    <!--        android:hint="@string/search"-->
    <!--        android:maxWidth="300dp"-->
    <!--        android:minWidth="200dp"-->
    <!--        app:layout_constraintEnd_toStartOf="@id/tvCalendar"-->
    <!--        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"-->
    <!--        tools:ignore="Autofill" />-->

    <com.metathought.food_order.casheir.ui.widget.CustomSearchView
        android:id="@+id/edtSearch"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/tvCalendar"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        app:search_hint="@string/search" />

    <com.metathought.food_order.casheir.ui.widget.CalendarTextView
        android:id="@+id/tvCalendar"
        style="@style/commonCalendarTextViewStyle"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/tvClearFilter"
        app:layout_constraintTop_toTopOf="@id/radioGroupFilter"
        tools:text="01 03, 2024 - 01 03, 2024" />

    <TextView
        android:id="@+id/tvClearFilter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:drawablePadding="10dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="15dp"
        android:singleLine="true"
        android:text="@string/clear_filter"
        android:textColor="@color/primaryColor"
        android:textSize="@dimen/_16ssp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCalendar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="6dp"
            android:background="@drawable/background_dialog"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal">

                <!--                <TextView-->
                <!--                    style="@style/FontLocalization"-->
                <!--                    android:layout_width="0dp"-->
                <!--                    android:layout_height="match_parent"-->
                <!--                    android:layout_weight="0.5"-->
                <!--                    android:gravity="center"-->
                <!--                    android:paddingHorizontal="10dp"-->
                <!--                    android:text="@string/customer_profile"-->
                <!--                    android:textColor="@color/black"-->
                <!--                    android:textSize="@dimen/_14ssp" />-->

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.8"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/customer_nickname"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.8"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/customer_account"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.6"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/amount"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.6"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/type"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />


                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.6"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/payment_method"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />


                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.6"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/status"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/time"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />

                <TextView
                    style="@style/FontLocalization"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.6"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="@string/action"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_14ssp" />
            </LinearLayout>

            <View style="@style/commonDividerStyle" />

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/refreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/background_dialog"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.scwang.smart.refresh.header.MaterialHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerviewMemberBalance"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:overScrollMode="never"
                    android:paddingBottom="16dp"
                    android:scrollbars="none"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="1"
                    tools:listitem="@layout/balance_list_items" />

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.scwang.smart.refresh.layout.SmartRefreshLayout>
        </LinearLayout>

        <include
            android:id="@+id/layoutEmpty"
            layout="@layout/layout_empty_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            tools:visibility="visible" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
