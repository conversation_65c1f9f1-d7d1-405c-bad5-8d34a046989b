<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layoutWrap"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/filter_table_background"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp"
    app:strokeColor="@android:color/transparent"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:paddingVertical="15dp">

        <TextView
            android:id="@+id/tvNickname"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/mainWhite"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="start"
            android:paddingHorizontal="10dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="客户昵称" />

        <TextView
            android:id="@+id/tvAccount"
            style="@style/FontLocalization"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:layout_weight="1"
            android:backgroundTint="@color/mainWhite"
            android:ellipsize="end"
            android:fontFamily="@font/roboto"
            android:gravity="end"
            android:paddingHorizontal="10dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="************" />

    </LinearLayout>
</com.google.android.material.card.MaterialCardView>