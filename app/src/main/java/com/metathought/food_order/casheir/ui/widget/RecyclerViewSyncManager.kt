package com.metathought.food_order.casheir.ui.widget

import androidx.recyclerview.widget.RecyclerView

/**
 * RecyclerView同步滚动管理器 - 支持多个RecyclerView同步
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class RecyclerViewSyncManager {

    private val syncPairs = mutableListOf<Pair<RecyclerView, RecyclerView>>()
    private val scrollingStates = mutableMapOf<RecyclerView, Boolean>()

    fun setupSync(mainRecyclerView: RecyclerView, syncRecyclerView: RecyclerView) {
        syncPairs.add(Pair(mainRecyclerView, syncRecyclerView))

        // 设置主RecyclerView的滚动监听
        mainRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (scrollingStates[recyclerView] != true) {
                    scrollingStates[syncRecyclerView] = true
                    syncRecyclerView.scrollBy(dx, dy)
                    scrollingStates[syncRecyclerView] = false
                }
            }
        })

        // 设置同步RecyclerView的滚动监听
        syncRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (scrollingStates[recyclerView] != true) {
                    scrollingStates[mainRecyclerView] = true
                    mainRecyclerView.scrollBy(dx, dy)
                    scrollingStates[mainRecyclerView] = false
                }
            }
        })
    }
}
