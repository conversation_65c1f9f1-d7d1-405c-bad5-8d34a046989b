package com.metathought.food_order.casheir.ui.widget

import androidx.recyclerview.widget.RecyclerView

/**
 * RecyclerView同步滚动管理器
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class RecyclerViewSyncManager {
    
    private var mainRecyclerView: RecyclerView? = null
    private var syncRecyclerView: RecyclerView? = null
    private var isScrollingFromMain = false
    private var isScrollingFromSync = false
    
    fun setupSync(mainRecyclerView: RecyclerView, syncRecyclerView: RecyclerView) {
        this.mainRecyclerView = mainRecyclerView
        this.syncRecyclerView = syncRecyclerView
        
        // 设置主RecyclerView的滚动监听
        mainRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrollingFromSync) {
                    isScrollingFromMain = true
                    syncRecyclerView.scrollBy(dx, dy)
                    isScrollingFromMain = false
                }
            }
        })
        
        // 设置同步RecyclerView的滚动监听
        syncRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrollingFromMain) {
                    isScrollingFromSync = true
                    mainRecyclerView.scrollBy(dx, dy)
                    isScrollingFromSync = false
                }
            }
        })
    }
}
