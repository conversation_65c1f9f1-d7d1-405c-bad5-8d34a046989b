package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.widget.HorizontalScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * 自定义RecyclerView，用于客户昵称列的同步滑动
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class SyncNameRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private var syncRecyclerView: RecyclerView? = null
    private var nameHeaderScrollView: HorizontalScrollView? = null
    private var isScrollingFromThis = false
    private var isScrollingFromSync = false
    private var currentScrollX = 0
    private val syncHandler = Handler(Looper.getMainLooper())
    private var syncRunnable: Runnable? = null

    /**
     * 设置需要同步滑动的RecyclerView（操作列）
     */
    fun setSyncRecyclerView(syncRecyclerView: RecyclerView) {
        this.syncRecyclerView = syncRecyclerView
        setupScrollSync()
    }

    /**
     * 设置需要同步滑动的表头ScrollView
     */
    fun setNameHeaderScrollView(nameHeaderScrollView: HorizontalScrollView) {
        this.nameHeaderScrollView = nameHeaderScrollView
    }

    private fun setupScrollSync() {
        // 监听操作列的滑动，同步到昵称列
        syncRecyclerView?.addOnScrollListener(object : OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (!isScrollingFromThis && dy != 0) {
                    scheduleSync()
                }
            }
        })
    }

    private fun syncNameScrollViews(scrollX: Int) {
        // 遍历昵称列中可见的ViewHolder
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            val nameScrollView = findHorizontalScrollView(child)
            nameScrollView?.scrollTo(scrollX, 0)
        }
    }

    private fun findHorizontalScrollView(view: android.view.View): HorizontalScrollView? {
        if (view is HorizontalScrollView) {
            return view
        }
        if (view is android.view.ViewGroup) {
            for (i in 0 until view.childCount) {
                val found = findHorizontalScrollView(view.getChildAt(i))
                if (found != null) return found
            }
        }
        return null
    }

    override fun onScrolled(dx: Int, dy: Int) {
        super.onScrolled(dx, dy)
        
        // 当昵称列垂直滚动时，确保新出现的item保持当前的水平滑动位置
        if (!isScrollingFromSync && dy != 0) {
            scheduleSync()
        }
    }

    override fun onChildAttachedToWindow(child: android.view.View) {
        super.onChildAttachedToWindow(child)
        // 当新的item添加到RecyclerView时，设置其滑动监听并同步位置
        val nameScrollView = findHorizontalScrollView(child)
        nameScrollView?.let { scrollView ->
            setupNameScrollListener(scrollView)
            // 使用保存的滑动位置来设置新item的位置
            scrollView.scrollTo(currentScrollX, 0)
        }
    }

    private fun setupNameScrollListener(nameScrollView: HorizontalScrollView) {
        // 优化滑动体验
        nameScrollView.apply {
            overScrollMode = android.view.View.OVER_SCROLL_NEVER
            isHorizontalFadingEdgeEnabled = false
            isVerticalFadingEdgeEnabled = false
        }
        
        // 设置滑动监听，当昵称列滑动时同步操作列
        nameScrollView.setOnScrollChangeListener { _, scrollX, _, _, _ ->
            if (!isScrollingFromSync) {
                isScrollingFromThis = true
                currentScrollX = scrollX
                // 同步表头滑动
                nameHeaderScrollView?.scrollTo(scrollX, 0)
                // 同步操作列的滑动
                syncOperationColumn(scrollX)
                // 同步其他昵称item的滑动
                syncNameScrollViews(scrollX)
                isScrollingFromThis = false
            }
        }
    }

    private fun syncOperationColumn(scrollX: Int) {
        // 同步操作列的滑动
        syncRecyclerView?.let { operationRecyclerView ->
            for (i in 0 until operationRecyclerView.childCount) {
                val child = operationRecyclerView.getChildAt(i)
                // 这里可以添加操作列的滑动逻辑，如果操作列也有HorizontalScrollView的话
                // 目前操作列是固定的，所以不需要滑动
            }
        }
    }

    /**
     * 延迟同步，避免频繁调用
     */
    private fun scheduleSync() {
        syncRunnable?.let { syncHandler.removeCallbacks(it) }
        syncRunnable = Runnable {
            syncNameScrollViews(currentScrollX)
        }
        syncHandler.postDelayed(syncRunnable!!, 50)
    }

    /**
     * 外部调用，用于同步滑动位置
     */
    fun syncToPosition(scrollX: Int) {
        if (!isScrollingFromThis) {
            isScrollingFromSync = true
            currentScrollX = scrollX
            syncNameScrollViews(scrollX)
            isScrollingFromSync = false
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        syncRunnable?.let { syncHandler.removeCallbacks(it) }
    }
}
