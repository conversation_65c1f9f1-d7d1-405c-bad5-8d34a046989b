package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.data.model.base.response_model.order.CouponActivityModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogDiscountActivityBinding
import com.metathought.food_order.casheir.ui.adapter.DiscountActivityListAdapter
import dagger.hilt.android.AndroidEntryPoint


/**
 *<AUTHOR>
 *@time  2024/8/25
 *@desc  生效的优惠活动列表
 **/
@AndroidEntryPoint
class DiscountActivityDialog : BaseDialogFragment() {
    companion object {
        private const val TAG = "DiscountActivityDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            list: List<CouponActivityModel>,
            isShowVip: Boolean,
            orderInfo: OrderedInfoResponse? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment =
                newInstance(
                    list,
                    isShowVip,
                    orderInfo
                )

            fragment.show(fragmentManager, TAG)
        }

        private fun newInstance(
            list: List<CouponActivityModel>,
            isShowVip: Boolean,
            orderInfo: OrderedInfoResponse? = null
        ): DiscountActivityDialog {
            val fragment = DiscountActivityDialog()
            fragment.list = list
            fragment.isShowVip = isShowVip
            fragment.orderInfo = orderInfo
            return fragment
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? DiscountActivityDialog
            fragment?.dismissAllowingStateLoss()
        }
    }

    private var binding: DialogDiscountActivityBinding? = null
    private var list: List<CouponActivityModel> = listOf()
    private var isShowVip = false
    private var orderInfo: OrderedInfoResponse? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogDiscountActivityBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initObserver()
        initListener()
    }

    private fun initObserver() {

    }

    private fun initView() {

    }


    private fun initListener() {
        binding?.apply {
            rvList.adapter = DiscountActivityListAdapter(list, isShowVip,orderInfo)

            topBar.setOnClickListener {
                dismissAllowingStateLoss()
            }

        }
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.4).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.3).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

}