package com.metathought.food_order.casheir.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PointF
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentManager
import com.lxj.xpopup.XPopup
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.databinding.ViewOrderDetailsBinding
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.getSourcePlatform
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.dialog.CancelReasonDialog
import com.metathought.food_order.casheir.ui.dialog.DiscountActivityDialog
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetail
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetailDialog
import com.metathought.food_order.casheir.ui.dialog.PackPriceDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ServiceFeeDetailDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountDetailDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.GiftProductsListDialog
import timber.log.Timber
import java.math.BigDecimal


class OrderDetailsView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var binding: ViewOrderDetailsBinding

    private var currentOrderedInfo: OrderedInfoResponse? = null

    private var orderedInfoAdapter: OrderedInfoAdapter? = null

    //    private var parentFragmentManager: FragmentManager? = null
    var onCloseListener: (() -> Unit)? = null
    var onBackListener: (() -> Unit)? = null

    init {
        val inflater = LayoutInflater.from(context)
        binding = ViewOrderDetailsBinding.inflate(inflater, this)


        orderedInfoAdapter =
            OrderedInfoAdapter(arrayListOf(), onItemClickListener = { index, item ->

            }, onWarnClick = { view, item ->
                if (fragmentManager != null) {
                    SingleDiscountDetailDialog.showDialog(
                        fragmentManager!!,
                        orderGood = item,
                        orderedInfoResponse = currentOrderedInfo
                    )
                }
            })

        binding.apply {
            binding.orderedInfoRecyclerView.adapter = orderedInfoAdapter

            btnCloseDetail.setOnClickListener {
                onCloseListener?.invoke()
            }

            btnBack.setOnClickListener {
                onBackListener?.invoke()
            }


            llDiscountActivity.setOnClickListener {
                if (!btnDiscountActivityPriceCue.isVisible) {
                    return@setOnClickListener
                }
                fragmentManager?.let {
                    DiscountActivityDialog.showDialog(
                        it,
                        currentOrderedInfo?.couponActivityList ?: listOf(),
                        tvVipPrice.isVisible,
                        currentOrderedInfo
                    )
                }
            }


            llPackPrice.setOnClickListener {
                if (!btnPackPriceCue.isVisible) {
                    return@setOnClickListener
                }
                val list = currentOrderedInfo?.goods?.map {
                    it.orderedGoodsConvertToGoods()
                }

                if (list.isNullOrEmpty()) {
                    return@setOnClickListener
                }
                fragmentManager?.let {
                    PackPriceDetailDialog.showDialog(it, list)
                    PackPriceDetailDialog.showDialog(
                        it,
                        list,
                        currentOrderedInfo?.conversionRatio,
                        TakeOutPlatformModel(
                            id = currentOrderedInfo?.id,
                            name = currentOrderedInfo?.deliveryPlatformName,
                            type = currentOrderedInfo?.deliveryPlatformCurrencyType
                        )
                    )
                }
            }


            llServiceFee.setOnClickListener {
                if (!btnServiceFeeCue.isVisible) {
                    return@setOnClickListener
                }
                val list = currentOrderedInfo?.goods?.map {
                    it.setServiceChargePercentage(currentOrderedInfo!!.getServicePercentage())
                    it.orderedGoodsConvertToGoods()
                }

                if (list.isNullOrEmpty()) {
                    return@setOnClickListener
                }

                fragmentManager?.let {
                    ServiceFeeDetailDialog.showDialog(it, list, currentOrderedInfo)
                }
            }


            llDiscount.setOnClickListener {
                if (!btnDiscountCue.isVisible) {
                    return@setOnClickListener
                }
                if (currentOrderedInfo?.getWholeDiscountReason().isNullOrEmpty()) {
                    return@setOnClickListener
                }
                val location = IntArray(2)
                btnDiscountCue.getLocationOnScreen(location)
                val x = location[0] + btnDiscountCue.width / 2
                val y = location[1]
                XPopup.Builder(context)
                    .hasShadowBg(false)
                    .isTouchThrough(true)
                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .atPoint(PointF(x.toFloat(), y.toFloat()))
                    .isCenterHorizontal(true)
                    .hasShadowBg(false) // 去掉半透明背景
                    .asCustom(
                        CustomBubbleAttachPopup(
                            context,
                            currentOrderedInfo?.getWholeDiscountReason()
                        )
                    )
                    .show()
            }


            llDiscountAmount.setOnClickListener {
                if (!btnDiscountAmountCue.isVisible) {
                    return@setOnClickListener
                }
                if (currentOrderedInfo?.getWholeDiscountReason().isNullOrEmpty()) {
                    return@setOnClickListener
                }
                val location = IntArray(2)
                btnDiscountAmountCue.getLocationOnScreen(location)
                val x = location[0] + btnDiscountAmountCue.width / 2
                val y = location[1]
                XPopup.Builder(context)
                    .hasShadowBg(false)
                    .isTouchThrough(true)
                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .atPoint(PointF(x.toFloat(), y.toFloat()))
                    .isCenterHorizontal(true)
                    .hasShadowBg(false) // 去掉半透明背景
                    .asCustom(
                        CustomBubbleAttachPopup(
                            context, currentOrderedInfo?.getWholeDiscountReason()
                        )
                    )
                    .show()
            }



            llTotalPrice.setOnClickListener {
                if (!btnTotalPriceDetail.isVisible) {
                    return@setOnClickListener
                }
                val list = currentOrderedInfo?.goods?.map {
                    it.setServiceChargePercentage(currentOrderedInfo!!.getServicePercentage())
                    it.orderedGoodsConvertToGoods()
                }

                if (list.isNullOrEmpty()) {
                    return@setOnClickListener
                }

                fragmentManager?.let {
                    OrderAmountDetailDialog.showDialog(
                        it,
                        orderAmountDetail = OrderAmountDetail(
                            goods = list,
                            orderedInfo = currentOrderedInfo
                        )
                    )
                }
            }
        }
    }


    private var fragmentManager: FragmentManager? = null

    fun setFragmentManager(fragmentManager: FragmentManager) {
        this.fragmentManager = fragmentManager
    }

    fun setTitle(orderNo: String) {
        binding.tvOrderNoTitle.text = orderNo
    }

    fun replaceorderedInfo(orderedInfo: OrderedInfoResponse? = null) {
        orderedInfoAdapter?.replaceData(
            OrderHelper.sortingGoodsWithMergeOrder(
                context,
                orderedInfo?.mergeOrderIds,
                orderedInfo?.acceptOrderIds,
                orderedInfo?.goods
            ),
            orderedInfo?.refundGoodsJson,
            orderedInfo
        )
    }
//    fun setParentFragmentManager(orderedInfo: OrderedInfoResponse? = null) {
//        this.
//    }

    @SuppressLint("SetTextI18n")
    fun setOrderInfo(ordered: OrderedInfoResponse?) {
        binding.apply {
            ordered?.let {
                currentOrderedInfo = it
                if (it.isTakeOut()) {
                    llTakeOutPlatform.isVisible = true
                    tvTakeOutPlatform.text = it.deliveryPlatformName
                    llTakeOutId.isVisible = true
                    tvTakeOutId.text = it.deliveryOrderNo
                } else {
                    llTakeOutPlatform.isVisible = false
                    llTakeOutId.isVisible = false
                }

                tvTableID.text = it.tableName

                llCustomerName.isVisible = it.getLastCustomerName().isNotEmpty()
                tvCustomerName.text = it.getLastCustomerName()

                llCustomerPhone.isVisible = !it.customerInfoVo?.mobile.isNullOrEmpty()
                tvCustomerPhone.text = it.customerInfoVo?.mobile

                llPeople.isVisible = !(it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString()
                    .isNullOrEmpty() && (it.customerInfoVo?.diningNumber ?: it.peopleNum) != 0
                tvCustomerNum.text =
                    (it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString() ?: ""

                tvCustomerTitle.isVisible =
                    llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible

//                tvCustomerTitle.isVisible =
//                    !it.customerInfoVo?.name.isNullOrEmpty() || !it.customerInfoVo?.mobile.isNullOrEmpty()

                tvOrderNo.text = it.orderNo

                llInvoiceNumber.isVisible = !it.invoiceNumber.isNullOrEmpty()
                tvInvoiceNumber.text = it.invoiceNumber

                tvOrderTime.text = it.createTime?.formatDate()
                llDiningTime.isVisible = !it.getDingTime().isNullOrEmpty()
                tvDiningTime.text = it.getDingTime().formatDate()

                llPickUpNo.isVisible = !it.pickupCode.isNullOrEmpty()
                tvPickUpNo.text = it.pickupCode

                context?.let { context ->
                    tvOrderType.text = it.getDiningStyleStr(context)
                    imgDelete.text = it.payStatus?.getPayText(context)
                    imgDelete.setTextColor(
                        ContextCompat.getColor(
                            context,
                            it.payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                        )
                    )
                    cardStatus.setCardBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            it.payStatus?.getPayTypeBackGroundColor()
                                ?: R.color.ordered_cancel_color
                        )
                    )

                    tvOrderBy.text = it.sourcePlatform?.getSourcePlatform(context)
                }

                tvOrderRemark.text =
                    if (it.note.isNullOrEmpty()) context.getString(R.string.none) else it.getFinalNote()

                llAntiSettlementReason.isVisible = !it.reverseReason.isNullOrEmpty()
                tvOrderAntiSettlement.text = it.reverseReason

                if (!it.isAfterPayStatus()) {
                    //支付前 总计要自己再计算一遍优惠券的金额
                    it.getTotalVipPriceBySelf()
                    it.getTotalPriceBySelf()
                }


                val subTotalPrice = it.getSubTotal()
                val serviceFeePrice = it.getRealServiceFeePrice()
                val vipServiceFeePrice = it.getVipServiceFeePrice()
                val vatPrice = it.getRealVatPrice()
                val vipVatPrice = it.getVipVat()

                var totalPrice = it.getRealPayPrice()
                var totalVipPrice = it.getFinalTotalVipPrice()

//                tvPackingAmount.text = it.getPackFee().priceFormatTwoDigitZero2()
                tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    it.getPackFee(),
                    it.isKhr()
                )
                llPackPrice.isVisible = it.getPackFee() > 0

                //小计
                tvSubtotal.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    subTotalPrice, it.isKhr()
                )

                titleVat.text =
                    "${context.getString(R.string.vat)}(${it.price?.getVatPercentage()}%)"
                //增值税
                tvVat.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    vatPrice, it.isKhr()
                )
                //服务费
                tvServiceFee.text = serviceFeePrice.priceFormatTwoDigitZero2()

                if (it.isTakeOut()) {
                    llCommission.isVisible = true
                    tvCommissionPrice.text =
                        "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                it.price?.getCommissionToLong() ?: 0L, it.isKhr()
                            )
                        }"
                } else {
                    llCommission.isVisible = false
                }


                //总计2
                tvTotalPrice2.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    totalPrice, it.isKhr()
                )
                tvTotalKhrPrice2.text = "៛${
                    FoundationHelper.usdConverToKhr(
                        it.conversionRatio ?: FoundationHelper.conversionRatio!!,
                        totalPrice
                    ).decimalFormatZeroDigit()
                }"


                //总计1
                tvTotalPrice.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    totalPrice, it.isKhr()
                )
                tvTotalKhrPrice.isVisible = true
                tvTotalKhrPrice.text = "៛${
                    FoundationHelper.usdConverToKhr(
                        it.conversionRatio ?: FoundationHelper.conversionRatio!!,
                        totalPrice
                    ).decimalFormatZeroDigit()
                }"

                if (it.isTakeOut() && it.isKhr()) {
                    tvTotalKhrPrice.isVisible = false
                }

                //会员价
                tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()

                val isShowVip = it.isShowVipPrice()
                tvVipPrice.isVisible = isShowVip

//                val isHasDiscountPrice = it.isHasDiscountPrice()

                //=======优惠活动金额================
                if (it.couponActivityList.isNullOrEmpty()) {
                    llDiscountActivity.isVisible = false
                } else {
                    btnDiscountActivityPriceCue.isVisible = true
                    if (it.couponActivityList?.size == 1) {
                        tvDiscountActivityTitle.text =
                            it.couponActivityList?.firstOrNull()?.activityLabelName
                    } else {
                        tvDiscountActivityTitle.text = context.getString(R.string.discount_activity)
                    }
                    if (it.isAfterPayStatus()) {
                        //如果是余额支付 取会员价
//                        if (it.payType == PayTypeEnum.USER_BALANCE.id) {
//                            tvDiscountActivityAmount.text = "-${
//                                it.getTotalVipCouponActivityAmount()?.priceFormatTwoDigitZero2()
//                            }"
//                        } else {
                        tvDiscountActivityAmount.text =
                            "-${it.getTotalCouponActivityAmount()?.priceFormatTwoDigitZero2()}"
//                        }

                    } else {
                        //
                        val discountActivityUnWeightList =
                            it.couponActivityList?.filter { it.weightMark == true } ?: listOf()
                        if (discountActivityUnWeightList.isNullOrEmpty()) {
                            tvDiscountActivityAmount.text =
                                "-${it.getTotalCouponActivityAmount()?.priceFormatTwoDigitZero2()}"
                        } else {
                            tvDiscountActivityAmount.text =
                                context.getString(R.string.to_be_confirmed)
                        }
                    }
                    llDiscountActivity.isVisible = true
                }
                //==============================

                //===================优惠券相关=================
                llCoupon.isVisible = false
                val isHasNeedWeight = ordered.isHasNeedProcess()
                tvCoupon.text = it.getCouponDesc(
                    context, true, isHasNeedWeight
                )
                tvViewCouponGiftGood.isVisible = false
                Timber.e("coupon: ${it.getCurrentCoupon()?.toJson()}")
                if (it.getCurrentCoupon() != null) {
                    llCoupon.isVisible = true
                    if (it.getZsCouponValid()) {
                        tvViewCouponGiftGood.isVisible = true
                        tvViewCouponGiftGood.setOnClickListener { view ->
                            fragmentManager?.let { manager ->
                                GiftProductsListDialog.showDialog(
                                    fragmentManager = manager,
                                    it.getGiftGoodsList()
                                )
                            }
                        }
                    }

//                    //非赠品券
//                    if (!it.isAfterPayStatus()) {
//                        //支付前 总计要自己再计算一遍优惠券的金额
//                        totalVipPrice -= (it.getCurrentCoupon()?.vipCouponPrice ?: 0L)
//                        if (totalVipPrice < 0L) {
//                            totalVipPrice = 0
//                        }
//                        tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()
//
//                        totalPrice -= (it.getCurrentCoupon()?.couponPrice ?: 0L)
//                        if (totalPrice < 0L) {
//                            totalPrice = 0
//                        }
//                        tvTotalPrice.text = totalPrice.priceFormatTwoDigitZero2()
//                    }
                }

                //======================优惠券================================

                if (isShowVip) {
//                    tvOriginalPrice.isVisible = false
                    llServiceFee.isVisible =
                        serviceFeePrice > 0 || vipServiceFeePrice > BigDecimal.ZERO
                    llVat.isVisible = vatPrice > 0 || vipVatPrice > BigDecimal.ZERO

                    if (it.isAfterPayStatus()) {
                        //如果是支付后的状态
                        llServiceFee.isVisible = serviceFeePrice > 0
                        llVat.isVisible = vatPrice > 0
                    }
                } else {
//                    tvOriginalPrice.isVisible = isHasDiscountPrice
//                    tvOriginalPrice.text = it.totalPrice?.priceFormatTwoDigitZero2()
                    tvVipPrice.isVisible = false

                    llServiceFee.isVisible = serviceFeePrice > 0
                    llVat.isVisible = vatPrice > 0
                }
                //判断是否有新版整单减免
                llDiscount.isVisible = false
                llDiscountAmount.isVisible = false
                btnDiscountCue.isVisible = false
                btnDiscountAmountCue.isVisible = false
                if (it.isSetWholeDiscount()) {
                    if (it.discountReduceActivity != null) {
                        if (it.discountReduceActivity?.isPercentDiscount() == true) {
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount()
                                        .times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvDiscountTitle.text =
                                "${context.getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        } else if (it.discountReduceActivity?.isFixedAmount() == true) {
                            //设置的是整单减免
                            llDiscountAmount.isVisible = true
                            val title = "${context.getString(R.string.discounts2)}"
                            val content = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceDollar()
                                        .times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvDiscountAmount.text = content
                            tvDiscountAmountTitle.text = title
                        }
                    } else {
                        if (it.getWholePercentDiscount() > BigDecimal.ZERO) {
                            //设置的是整单百分比折扣
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"
                            tvDiscountTitle.text =
                                "${context.getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        }

                        if (it.isSetCustomizeReduce()) {
                            //设置的是整单减免
                            llDiscountAmount.isVisible = true
                            var content = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceDollar().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"
                            if (it.getNormalWholeReduceKhr() > BigDecimal.ZERO || it.getVipWholeReduceKhr() > BigDecimal.ZERO) {
                                content =
                                    "-៛${it.getNormalWholeItemReduceKhr().decimalFormatZeroDigit()}"
                            }
                            tvDiscountAmount.text = content
                            tvDiscountAmountTitle.text = context.getString(R.string.discounts2)
                            if (it.isAfterPayStatus()) {
                                //如果支付完了以后 有设置值才显示
                                if (it.getNormalWholeReduceDollar() == BigDecimal.ZERO && it.getNormalWholeReduceKhr() == BigDecimal.ZERO) {
                                    llDiscountAmount.isVisible = false
                                }
                            }
                        }
                    }
                    btnDiscountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                    btnDiscountAmountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                }


                //normal
                llActualReceiveAmount.isGone = true
                llVatRefundAmount.isGone = true
                llPartialRefundAmount.isGone = true
                llTotalPrice2.isGone = true
                llRefundTime.isGone = true
                llPaymentTime.isGone = it.payTime.isNullOrEmpty()
                tvPaymentTime.text = it.payTime?.formatDate()
                llPaymentMethod1.isVisible = false
                llPaymentMethod2.isVisible = false
                llChange.isVisible = false
                llPaymentMethodPayAmount1.isVisible = false
                llPaymentMethodPayAmount2.isVisible = false
                llTotalPrice.isVisible = true
                llRefundAmount.isGone = true
                llCancelTime.isGone = true
                llPartialRefundPackFee.isVisible = false
                llPartialRefundServiceFee.isVisible = false
                llCancelReason.isGone = true
                btnTotalPriceDetail.isVisible = false
                llRefundPayType.isVisible = false

                llUserAccount.isVisible = false
                llUserName.isVisible = false
                llCreditReason.isVisible = false
                llCancelCreditReason.isVisible = false

                llRepaymentTime.isVisible = !it.repaymentTime.isNullOrEmpty()
                tvRepaymentTime.text = it.repaymentTime?.formatDate()

                when (it.payStatus) {
                    //挂账-未支付
                    OrderedStatusEnum.CREDIT_UNPAID.id -> {
                        updatePaymentMethod(ordered)
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                    }
                    //挂账-已支付
                    OrderedStatusEnum.CREDIT_PAID.id -> {
                        updatePaymentMethod(ordered)
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                    }

                    OrderedStatusEnum.UNPAID.id -> {
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //un paid
                        btnTotalPriceDetail.isVisible = true
                    }

                    OrderedStatusEnum.PAID.id -> {

                        llAntiSettlementReason.isVisible = false
                        //paid
                        updatePaymentMethod(ordered)

                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        if (it.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                    }

                    OrderedStatusEnum.PARTIAL_REFUND.id -> {
//                        tvCustomerTitle.isVisible =
//                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        llAntiSettlementReason.isVisible = false

                        //partial refund
                        llTotalPrice.isGone = true
                        llTotalPrice2.isVisible = true
                        updatePaymentMethod(ordered)

                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false


                        llRefundTime.isVisible = true
                        tvRefundTime.text = it.refundDateTime?.formatDate()

                        llRefundPayType.isVisible = true
                        if (it.isPayByMix()) {
                            if (it.oneTimeRefund == true) {
                                tvRefundPayType.text =
                                    "${context.getString(R.string.balance)}\n${context.getString(R.string.offline_refund)}"
                            } else {
                                tvRefundPayType.text = context.getString(R.string.offline_refund)
                            }

                        } else {
                            tvRefundPayType.text = ordered?.getRefundType(context)
                        }

                        llActualReceiveAmount.isVisible = true
                        tvActualReceiveAmount.text = FoundationHelper.getPriceStrByUnit(
                            it.conversionRatio ?: FoundationHelper.conversionRatio,
                            (totalPrice - (it.refundPrice
                                ?: 0)), it.isKhr()
                        )

                        val vatRefundAmount = it.getVatRefundAmount()
                        llVatRefundAmount.isVisible = vatRefundAmount > 0
                        tvVatRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                vatRefundAmount, it.isKhr()
                            )
                        }"

                        val packRefundAmount = it.getPackRefundAmount()
                        llPartialRefundPackFee.isVisible = packRefundAmount > 0
                        tvPartialRefundPackFee.text =
                            "-${packRefundAmount.priceFormatTwoDigitZero2()}"

                        llPartialRefundAmount.isVisible = true
                        tvPartialRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                it.getPartialRefundAmount(), it.isKhr()
                            )
                        }"

                        if (!it.isTakeAway()) {
                            val serviceFeeRefundAmount = it.getServiceFeeRefundAmount()
                            llPartialRefundServiceFee.isVisible = serviceFeeRefundAmount > 0
                            tvPartialRefundServiceFee.text =
                                "-${serviceFeeRefundAmount.priceFormatTwoDigitZero2()}"
                        }

                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        if (it.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                    }

                    OrderedStatusEnum.FULL_REFUND.id -> {
//                        tvCustomerTitle.isVisible =
//                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible

                        llTotalPrice2.isVisible = true
                        llTotalPrice.isGone = true
                        updatePaymentMethod(ordered)
                        llRefundTime.isVisible = true
                        tvRefundTime.text = it.refundDateTime?.formatDate()
                        //refund
                        llRefundAmount.isVisible = true
                        tvRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                it.refundPrice, it.isKhr()
                            )
                        }"
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        llRefundPayType.isVisible = true
                        if (it.isPayByMix()) {
                            if (it.oneTimeRefund == true) {
                                tvRefundPayType.text =
                                    "${context.getString(R.string.balance)}\n${context.getString(R.string.offline_refund)}"
                            } else {
                                tvRefundPayType.text = context.getString(R.string.offline_refund)
                            }

                        } else {
                            tvRefundPayType.text = ordered?.getRefundType(context)
                        }

                        if (it.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                        if (it.isTakeOut() && it.isKhr()) {
                            tvTotalKhrPrice2.isVisible = false
                        } else {
                            tvTotalKhrPrice2.isVisible = true
                        }
                    }

                    OrderedStatusEnum.CANCEL_ORDER.id -> {
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname
                        //canceled
//                        tvCustomerTitle.isVisible =
//                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible

                        llCancelTime.isVisible = !it.cancelTime.isNullOrEmpty()
                        tvCancelTime.text = it.cancelTime?.formatDate()

                        val cancelReason = it.cancelReason ?: ""
                        llCancelReason.isVisible = cancelReason.isNotEmpty()

                        tvCancelReason.originalText = ""
                        tvCancelReason.setOpenSuffix(context.getString(R.string.review))
                        tvCancelReason.setOpenSuffixColor(context.getColor(R.color.primaryColor))
                        tvCancelReason.postDelayed({
                            Timber.e("tvCancelReason2.measuredWidth ${tvCancelReason.measuredWidth}")
                            tvCancelReason.initWidth(tvCancelReason.measuredWidth)
                            tvCancelReason.setMaxLines(1)
                            tvCancelReason.originalText = cancelReason
                        }, 300)
                        tvCancelReason.setOnClickListener {
                            fragmentManager?.let { manager ->
                                CancelReasonDialog.showDialog(
                                    fragmentManager = manager,
                                    cancelReason
                                )
                            }
                        }

                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false

                        showTobeProcess(ordered)
                        if (it.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                    }

                    OrderedStatusEnum.BE_CONFIRM.id -> {
                        //confirm
                        showTobeProcess(ordered)
                        btnTotalPriceDetail.isVisible = true
                    }

                    OrderedStatusEnum.PREORDER.id -> {
//                        tvCustomerTitle.isVisible =
//                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        llAntiSettlementReason.isVisible = false


                        updatePaymentMethod(ordered)
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        if (it.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                    }

                    OrderedStatusEnum.TO_BE_CONFIRM.id -> {
                        showTobeProcess(ordered)
                        btnTotalPriceDetail.isVisible = true
                    }
                }
            }
        }
    }


    private fun updatePaymentMethod(orderedInfo: OrderedInfoResponse?) {
        binding?.apply {
            context?.let { context ->
                if (orderedInfo?.isPayByMix() == true) {
                    //如果是混合支付
                    llPaymentMethod1.isVisible = true
                    llPaymentMethod2.isVisible = true
                    llPaymentMethodPayAmount1.isVisible = true
                    llPaymentMethodPayAmount2.isVisible = true
                    paymentMethod1.text = context.getString(R.string.payment_method_1)
                    tvPaymentMethod1.text = context.getString(R.string.pay_by_balance)

                    tvPaymentMethodPayAmount1.text =
                        orderedInfo.balancePayAmount?.priceFormatTwoDigitZero1()

                    paymentMethod2.text = context.getString(R.string.payment_method_2)
                    tvPaymentMethod2.text = orderedInfo?.getOfflinePayMethod(context)
                    if (orderedInfo?.isCash() == true) {
                        paymentMethodPayAmount2.text = context.getString(R.string.cash_collection)
                        tvPaymentMethodPayAmount2.text =
                            orderedInfo.getReceiveAmountToTicket()
                    } else {
                        paymentMethodPayAmount2.text = context.getString(R.string.pay_amount)
                        tvPaymentMethodPayAmount2.text =
                            orderedInfo.offlinePayAmount?.priceFormatTwoDigitZero1()
                    }
                } else {
                    llPaymentMethod1.isVisible = true
                    paymentMethod1.text = context.getString(R.string.payment_method)
                    tvPaymentMethod1.text = orderedInfo?.getPaymentMethod(context)
                    if (orderedInfo?.isCash() == true) {
                        llPaymentMethodPayAmount2.isVisible = true
                        paymentMethodPayAmount2.text = context.getString(R.string.cash_collection)
                        tvPaymentMethodPayAmount2.text =
                            orderedInfo.getReceiveAmountToTicket()
                    }
                }

                if (orderedInfo?.isCash() == true) {
                    //如果是现金支付 显示收取现金 和找零
                    llChange.isVisible = true
                    tvChange.text = orderedInfo?.getChangeAmountToTicket()
                }
            }
        }
    }


    private fun showTobeProcess(ordered: OrderedInfoResponse?) {
        //如果有称重的，且已经全部称重
        val isHasNeedProcess = ordered?.isHasNeedProcess() ?: false
        ordered?.let {
            binding.apply {
                var unProcessNum = 0
                var unProcessServiceWhiteGoods = 0

                if (it.needShowUnProcessState()) {
                    it.goods?.forEach { good ->
                        if (!good.isHasProcessed()) {
                            unProcessNum += 1
                            if (good.serviceChargeWhitelisting == true) {
                                unProcessServiceWhiteGoods += 1
                            }
                        }
                    }
                }
                if (isHasNeedProcess) {
                    tvSubtotal.text = context.getString(R.string.to_be_confirmed)
                    tvTotalPrice.text = context.getString(R.string.to_be_confirmed)

                    tvDiscount.text = context.getString(R.string.to_be_confirmed)
                    tvDiscountAmount.text = context.getString(R.string.to_be_confirmed)

                    tvVat.text = context.getString(R.string.to_be_confirmed)
                    llVat.isVisible =
                        (ordered.price?.vatPercentage ?: BigDecimal(0)) > BigDecimal(0)
                    tvTotalKhrPrice.isVisible = false
                    tvVipPrice.isVisible = false

                    if (!ordered.isTakeAway()) {
                        if (unProcessNum > unProcessServiceWhiteGoods && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                            tvServiceFee.text = context.getString(R.string.to_be_confirmed)
                            llServiceFee.isVisible = true
                        }
                    }
                }
            }
        }
    }
}