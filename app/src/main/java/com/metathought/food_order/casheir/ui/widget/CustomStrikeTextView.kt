package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.metathought.food_order.casheir.R

class CustomStrikeTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private var strikeWidth = 2f // 默认中划线宽度
    private var strikeColor = currentTextColor // 默认中划线颜色

    init {
        // 从XML属性中获取自定义参数
        context.obtainStyledAttributes(attrs, R.styleable.CustomStrikeTextView).apply {
            strikeWidth = getDimension(R.styleable.CustomStrikeTextView_strikeWidth, 2f)
            strikeColor = getColor(R.styleable.CustomStrikeTextView_strikeColor, currentTextColor)
            recycle()
        }
        // 启用中划线
        paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
//
//        // 获取文本边界和画笔
//        val paint = paint
//        val text = text.toString()
//        val textWidth = paint.measureText(text)
//        val textHeight = paint.descent() - paint.ascent()
//
//        // 计算中划线位置
//        val startX = paddingLeft.toFloat()
//        val stopX = startX + textWidth
//        val y = height / 2f + textHeight / 4f

        // 绘制自定义中划线
        paint.color = strikeColor
        paint.strokeWidth = 100f
//        canvas.drawLine(startX, y, stopX, y, paint)
    }
}