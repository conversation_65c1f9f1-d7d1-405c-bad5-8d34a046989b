package com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate


import android.content.Context
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.constant.PrinterFontSizeEnum
import com.metathought.food_order.casheir.utils.DisplayUtils

data class InformationShow(
    @SerializedName("showNote")
    val showNote: Boolean?,
    @SerializedName("showServicePhone")
    val showServicePhone: Boolean?,
    @SerializedName("showSlogan")
    val showSlogan: Boolean?,
    @SerializedName("showTableName")
    val showTableName: Boolean?,

    @SerializedName("showCompanyName")
    val showCompanyName: Boolean?,
    @SerializedName("showCompanyTaxNumber")
    val showCompanyTaxNumber: Boolean?,
    @SerializedName("showCompanyAddress")
    val showCompanyAddress: Boolean?,
    @SerializedName("showCompanyContactNumber")
    val showCompanyContactNumber: Boolean?,
    @SerializedName("showCompanyContactEmail")
    val showCompanyContactEmail: Boolean?,

    @SerializedName("showKhqrCode")
    val showKhqrCode: Boolean?,

    /**
     * 是否显示passApp优惠码
     */
    @SerializedName("showPassAppPromotionCode")
    val showPassAppPromotionCode: Boolean?,

    /**
     * 显示商户名称,默认勾选
     */
    @SerializedName("showMerchantName")
    val showMerchantName: Boolean? = true,

    /**
     * 标签打印是否显示规格小料
     */
    @SerializedName("showGoodsSpec")
    val showGoodsSpec: Boolean? = true,

    /**
     * 标签打印是否显示完成度
     */
    @SerializedName("showOrderComplete")
    val showOrderComplete: Boolean? = true,

    /**
     * 标签打印是否显示取餐码
     */
    @SerializedName("showMealCode")
    val showMealCode: Boolean? = true,


    /**
     * 取餐码字号
     */
    @SerializedName("mealCodeFontSize")
    var mealCodeFontSize: Int? = 2,
    /**
     * 餐桌名字号
     */
    @SerializedName("tableNameFontSize")
    var tableNameFontSize: Int? = 2,
    /**
     * 预定时间字号
     */
    @SerializedName("dinningTimeFontSize")
    var dinningTimeFontSize: Int? = 2,
    /**
     * 商品名字号
     */
    @SerializedName("productNameFontSize")
    var productNameFontSize: Int? = 2,
    /**
     * 商品数量字号
     */
    @SerializedName("productNumFontSize")
    var productNumFontSize: Int? = 2,

    /**
     * 是否显示收银员名字
     */
    @SerializedName("showCashierName")
    var showCashierName: Boolean? = false,

    /**
     * 显示订单类型
     */
    @SerializedName("showOrderType")
    var showOrderType: Boolean? = false,

    /**
     * 显示商家logo
     */
    @SerializedName("showMerchantLogo")
    var showMerchantLogo: Boolean? = false,

    /**
     * 显示订单号
     */
    @SerializedName("showOrderNo")
    var showOrderNo: Boolean? = false,


    /**
     * 显示支付时间
     */
    @SerializedName("showPayTime")
    var showPayTime: Boolean? = false,

    /**
     * 显示订单号
     */
    @SerializedName("showOrderSource")
    var showOrderSource: Boolean? = false,


    /**
     * 显示顾客类型
     */
    @SerializedName("showCustomerTye")
    var showCustomerTye: Boolean? = false,


    /**
     * 是否显示人数
     */
    @SerializedName("showNumberOfPax")
    var showNumberOfPax: Boolean? = false,


    /**
     * 是否显示顾客姓名
     */
    @SerializedName("showCustomerName")
    var showCustomerName: Boolean? = false,


    /**
     * 展示汇率换算
     */
    @SerializedName("showExchangeRateConversion")
    var showExchangeRateConversion: Boolean? = false,

    /**
     * 订单金额大小
     */
    @SerializedName("totalAmountFontSize")
    var totalAmountFontSize: Int? = 2,

    /**
     * 找零金额大小
     */
    @SerializedName("changeAmountFontSize")
    var changeAmountFontSize: Int? = 2,

    /**
     * 展示退菜信息
     */
    @SerializedName("showRemoveGoods")
    var showRemoveGoods: Boolean? = false,

    /**
     * 厨打备注大小
     */
    @SerializedName("cookTicketMemoFontSize")
    var cookTicketMemoFontSize: Int? = 2,

//    /**
//     * 开启自动打印结账小票
//     */
//    @SerializedName("isAutoCheckoutTicket")
//    var isAutoCheckoutTicket: Boolean? = false,

    /**
     * 商品编码
     */
    @SerializedName("showGoodsCode")
    var showGoodsCode: Boolean? = false,

    /**
     * 商品价格
     */
    @SerializedName("showGoodsPrice")
    var showGoodsPrice: Boolean? = false,

    /**
     * 是否显示交接班小票的商品信息
     */
    @SerializedName("showGoodsReport")
    val showGoodsReport: Boolean?,


    /**
     * 结账单 店铺名字显示样式  SINGLE_LANGUAGE 单语言展示 MULTI_LANGUAGE_HORIZONTAL 多语言并排展示 MULTI_LANGUAGE_VERTICAL 多语言上下行展示
     */
    @SerializedName("merchantNameDisplayMode")
    val merchantNameDisplayMode: String?,

    ) {
    private fun getFontSizeByType(context: Context, type: Int): Float {

        return when (type) {
            PrinterFontSizeEnum.SMALL.id -> {
                DisplayUtils.px2sp(context, 22f).toFloat()
            }

            PrinterFontSizeEnum.STANDARD.id -> {
                DisplayUtils.px2sp(context, 22f).toFloat()
            }

            PrinterFontSizeEnum.BIG.id -> {
//                32f
                DisplayUtils.px2sp(context, 30f).toFloat()
            }

            PrinterFontSizeEnum.BIGGER.id -> {
//                40f
                DisplayUtils.px2sp(context, 38f).toFloat()
            }

            else -> {
                DisplayUtils.px2sp(context, 22f).toFloat()
            }
        }
    }

    fun getMealCodeFontRealSize(context: Context): Float {
        return getFontSizeByType(context, mealCodeFontSize ?: PrinterFontSizeEnum.STANDARD.id)
    }

    fun getTableNameFontRealSize(context: Context): Float {
        return getFontSizeByType(context, tableNameFontSize ?: PrinterFontSizeEnum.STANDARD.id)
    }

//    fun getDinningTimeFontRealSize(): Float {
//        return getFontSizeByType(dinningTimeFontSize ?: PrinterFontSizeEnum.STANDARD.id)
//    }

    fun getProductNameFontRealSize(context: Context): Float {
        return getFontSizeByType(context, productNameFontSize ?: PrinterFontSizeEnum.STANDARD.id)
    }

    fun getProductNumFontRealSize(context: Context): Float {
        return getFontSizeByType(context, productNumFontSize ?: PrinterFontSizeEnum.STANDARD.id)
    }

    fun getChangeAmountFontRealSize(context: Context): Float {
        return getFontSizeByType(context, changeAmountFontSize ?: PrinterFontSizeEnum.BIGGER.id)
    }

    fun getTotalAmountFontRealSize(context: Context): Float {
        return getFontSizeByType(context, totalAmountFontSize ?: PrinterFontSizeEnum.BIGGER.id)
    }

    fun getCookTicketMemoFontRealSize(context: Context): Float {
        return getFontSizeByType(context, cookTicketMemoFontSize ?: PrinterFontSizeEnum.BIGGER.id)
    }

    fun isShowRemoveGoods(): Boolean {
        return showRemoveGoods == true
    }

    fun isShowGoodsReport(): Boolean {
        return showGoodsReport == true
    }





}