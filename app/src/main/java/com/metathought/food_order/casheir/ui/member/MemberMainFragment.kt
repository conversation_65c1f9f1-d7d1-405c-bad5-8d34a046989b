package com.metathought.food_order.casheir.ui.member

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RadioButton
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.databinding.FragmentMemberBinding
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.member.balace.MemberBalanceFragment
import com.metathought.food_order.casheir.ui.member.credit.MemberCreditFragment
import com.metathought.food_order.casheir.ui.member.overview.MemberOverviewFragment
import com.metathought.food_order.casheir.ui.member.submember.MemberListFragment
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import dagger.hilt.android.AndroidEntryPoint



@AndroidEntryPoint
class MemberMainFragment : BaseFragment() {
    companion object {
        fun newInstance() = MemberMainFragment()
    }

//    private val viewModel: MemberMainSocketViewModel by viewModels()

    private var _binding: FragmentMemberBinding? = null
    private val binding get() = _binding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMemberBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initView()
        initListener()

//        initObserverSocket()
    }

    private var balanceBundle: Bundle? = null

    private fun initListener() {
        binding?.apply {
            radioGroupFilter.setOnCheckedChangeListener { group, checkedId ->

                when (checkedId) {
                    R.id.radioBalance -> {
                        replaceFragment(MemberBalanceFragment(), balanceBundle)
                        balanceBundle = null
                    }

                    R.id.radioMember -> {
                        replaceFragment(MemberListFragment())
                    }

                    R.id.radioOverview -> {
                        replaceFragment(MemberOverviewFragment())
                    }

                    R.id.radioCredit -> {
                        replaceFragment(MemberCreditFragment())
                    }
                }
            }

        }
    }


//    private fun initObserverSocket() {
//        viewModel.liveDataRespose.observe(viewLifecycleOwner) {
//            when (it) {
//                is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
//                is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
//                is WebSocket.Event.OnConnectionClosing -> Timber.e(
//                    "closing connection.."
//                )
//
//                is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
//                is WebSocket.Event.OnMessageReceived -> {
//                    if (it.message is Message.Text) {
//                        if ((it.message as Message.Text).value == "ping")
//                            viewModel.testingWebsocketSendMessage()
//                        else {
//                            try {
//                                Timber.e("ws ==> ${(it.message as Message.Text).value}")
//                                val socketModel = Gson().fromJson(
//                                    (it.message as Message.Text).value,
//                                    SocketModel::class.java
//                                )
//                                socketModel.cmd?.let { cmd ->
//
//                                    when (cmd) {
//                                        29 -> {
//                                            //获取Usb 打印配置
//                                            val currentFragment =
//                                                parentFragmentManager.fragments.firstOrNull()
//                                            if (currentFragment != null) {
//                                                (currentFragment as MainDashboardFragment).getUsbPrinterInfo()
//                                            } else {
//
//                                            }
//                                        }
//
//                                        else -> {}
//                                    }
//                                }
//                            } catch (e: Exception) {
//                                e.printStackTrace()
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }


    private fun initView() {
        binding?.apply {
            context?.let {
                replaceFragment(MemberOverviewFragment())
            }
        }
    }

    fun replaceFragment(fragment: BaseFragment, bundle: Bundle? = null) {
        fragment.arguments = bundle
        childFragmentManager.beginTransaction()
            .replace(R.id.fragmentMember, fragment).commit()

    }

    fun replaceBalanceFragment(bundle: Bundle? = null) {
        balanceBundle = bundle
        binding?.apply {
            //这样调用防止 setOnCheckedChangeListener 触发多次
            (radioGroupFilter.getChildAt(2) as RadioButton).isChecked = true
        }
    }


    //Socket
    override fun onResume() {
//        viewModel.startLifeCycle()
//        viewModel.connectWebsocket()
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        super.onResume()
    }

    override fun onPause() {
//        viewModel.destroylifeCycle()
        super.onPause()
    }

    private fun wsHandel(event: WebSocket.Event) {

    }

}