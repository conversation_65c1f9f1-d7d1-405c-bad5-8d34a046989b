package com.metathought.food_order.casheir.ui.member.balace.topup

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.google.gson.Gson
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponTemplateSDK
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeTier
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeTierCouponTemplate
import com.metathought.food_order.casheir.databinding.DialogCustomTopupBinding
import com.metathought.food_order.casheir.extension.NumberInputFilter
import com.metathought.food_order.casheir.extension.isZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.setDecimalFormat
import com.metathought.food_order.casheir.extension.setEnableWithAlpha
import com.metathought.food_order.casheir.filter.CashierInputFilter
import com.metathought.food_order.casheir.ui.adapter.RechargeGiftCouponsAdapter
import com.metathought.food_order.casheir.ui.dialog.BaseDialogFragment
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.lang.Integer.max
import java.lang.Integer.min
import java.math.BigDecimal


/**
 * 自定义充值Dialog
 *
 * @constructor Create empty Topup balance dialog
 */

@AndroidEntryPoint
class CustomTopupDialog : BaseDialogFragment() {
    private var binding: DialogCustomTopupBinding? = null
    private var callBackPayClick: ((ConsumerRechargeTier) -> Unit)? = null
    private var consumerRechargeTier: ConsumerRechargeTier? = null
    private val viewModel: CustomTopupViewModel by viewModels()

    private val adapter = RechargeGiftCouponsAdapter()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogCustomTopupBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(false)
        openKeyBoardListener()
        onTouchOutSide(binding?.layoutMain)
        initData()
        initObserver()
        initListener()
    }


    private fun initObserver() {
        viewModel.uiListState.observe(viewLifecycleOwner) {
            binding?.apply {
                if (it.showEnd) {
                    if (it.isRefresh != false) {
//                        layoutEmptyList.root.isVisible = true
                        adapter.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    isShowEmpty()
                }
                it.showError?.let { error ->
                    if (error.isNotEmpty())
                        Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
                }

                it.showSuccess?.let { response ->
//                    layoutEmptyList.root.isVisible = false
                    if (it.isRefresh != false) {
                        adapter.replaceData(response.records?.map { template ->
                            buildRechargeTierCouponTemplate(template)
                        })
                        refreshLayout.finishRefresh()

                    } else {
                        adapter.addData(response.records?.map { template ->
                            buildRechargeTierCouponTemplate(template)
                        })
                        refreshLayout.finishLoadMore()
                    }
                    isShowEmpty()
                }
            }
        }
    }

    private fun isShowEmpty() {
        binding?.apply {
            if (adapter.itemCount == 0) {
                layoutEmpty.root.isVisible = true
                layoutEmpty.tvEmptyText.setText(getString(R.string.no_coupon))
                refreshLayout.isVisible = false
            } else {
                layoutEmpty.root.isVisible = false
                refreshLayout.isVisible = true
            }
        }
    }

    private fun buildRechargeTierCouponTemplate(template: CouponTemplateSDK): RechargeTierCouponTemplate {
        val couponTemplate =
            consumerRechargeTier?.rechargeTierCouponTemplateList
                ?.find { it.couponTemplate?.id == template.id }
        var num = 1
        if (couponTemplate != null) {
            num = couponTemplate.num ?: 0
            adapter.addSelectedCoupon(template.id ?: "")
        }
        return RechargeTierCouponTemplate(
            couponTemplate = template,
            num = num
        )
    }

    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * PERCENT_85).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_85).toInt()
            dialog?.window?.setLayout(ViewGroup.LayoutParams.WRAP_CONTENT, screenHeight)
        }
    }

    private fun initData() {
        arguments?.let {
            consumerRechargeTier =
                Gson().fromJson(it.getString(CONTENT), ConsumerRechargeTier::class.java)
        }
        binding?.apply {
            rvRechargeTierPage.adapter = adapter
            btnConfirm.setEnableWithAlpha(false)
            edtTopUpAmount.setDecimalFormat()
            edtExtraBonus.filters = arrayOf(NumberInputFilter(7, 2))
            if (consumerRechargeTier != null) {
                val amount = consumerRechargeTier!!.amount?.priceFormatTwoDigitZero3()
                edtTopUpAmount.setText(amount)
                edtTopUpAmount.setSelection(edtTopUpAmount.length())
                Timber.d("edtTopUpAmount:${edtTopUpAmount.text.toString()}  amount:$amount")
                edtExtraBonus.setText(consumerRechargeTier!!.giftAmount?.priceFormatTwoDigitZero3())
                edtExtraBonus.setSelection(edtExtraBonus.length())
                updateConfirmEnable()
            }
        }
        viewModel.getRechargeGiftCouponsPage()
    }

    private fun updateConfirmEnable() {
        binding?.apply {
            val amount = edtTopUpAmount.text.toString().toDoubleOrNull() ?: 0.0
            if (amount < 5) {
                btnConfirm.setEnableWithAlpha(false)
                textInputLayoutAmount.error =
                    getString(R.string.cannot_be_less_than_s, "5")
                textInputLayoutAmount.isErrorEnabled = true
            } else if (amount > 99999.99) {
                btnConfirm.setEnableWithAlpha(false)
                textInputLayoutAmount.error =
                    getString(R.string.exceeded_amount_maximum_top_up_of_500_allowed)
                textInputLayoutAmount.isErrorEnabled = true
            } else {
                btnConfirm.setEnableWithAlpha(true)
                textInputLayoutAmount.error = ""
                textInputLayoutAmount.isErrorEnabled = false
            }
        }
    }

    private fun initListener() {
        binding?.apply {
            refreshLayout.setEnableRefresh(false)
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    viewModel.getRechargeGiftCouponsPage(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    viewModel.getRechargeGiftCouponsPage(false)
                }

            })
            edtTopUpAmount.filters = arrayOf(CashierInputFilter(false, 100000, false))
            edtTopUpAmount.addTextChangedListener { s ->
                s?.let {
                    if (it.isNotEmpty() && it.toString().first() == '.') {
                        s.replace(0, 1, "0.")
                    }
                    Timber.e("it:${it}")
                    if (it.isNotEmpty() && !it.startsWith("0.") && it.startsWith("0") && it.length > 1) {
                        s.replace(0, 1, "")
                    }
                }
                if (s?.isEmpty() == true) {
                    btnConfirm.setEnableWithAlpha(false)
                } else {
                    updateConfirmEnable()
                }
            }

            btnClose.setOnClickListener {
                dismissAllowingStateLoss()
            }
            btnConfirm.setOnClickListener {
                val giftAmountVal = edtExtraBonus.text.toString().toBigDecimalOrNull()
                    ?.times(BigDecimal(100)) ?: BigDecimal.ZERO
                val selectedCoupons = adapter.getSelectedCoupons()
                selectedCoupons.map { it.num = max((it.num ?: 0), 1) }
                if (consumerRechargeTier == null) {
                    consumerRechargeTier = ConsumerRechargeTier(
                        id = -1,
                        storeId = null,
                        name = null,
                        amount = edtTopUpAmount.text.toString().toBigDecimalOrNull()
                            ?.times(BigDecimal(100)),
                        giftAmount = if (giftAmountVal.isZero()) null else giftAmountVal,
                        status = null,
                        isDefault = null,
                        remark = null,
                        createTime = null,
                        rechargeTierCouponTemplateList = selectedCoupons
                    )
                } else {
                    consumerRechargeTier!!.apply {
                        id = -1
                        amount = edtTopUpAmount.text.toString().toBigDecimalOrNull()
                            ?.times(BigDecimal(100))
                        giftAmount = if (giftAmountVal.isZero()) null else giftAmountVal
                        rechargeTierCouponTemplateList = selectedCoupons
                    }
                }
                callBackPayClick?.invoke(consumerRechargeTier!!)
                dismissAllowingStateLoss()
            }

        }
    }

    companion object {
        private const val TAG = "CustomTopupDialog"
        private const val CONTENT = "CONTENT"
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            callBackClickListener: ((ConsumerRechargeTier) -> Unit)? = null,
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(content = content, callBackClickListener)
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment = fragmentManager.findFragmentByTag(TAG) as? CustomTopupDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            content: String? = null,
            callBackClickListener: ((ConsumerRechargeTier) -> Unit)? = null,
        ): CustomTopupDialog {
            val args = Bundle()
            content?.let { args.putString(CONTENT, it) }
            val fragment = CustomTopupDialog()
            fragment.arguments = args
            fragment.callBackPayClick = callBackClickListener
            return fragment
        }
    }

}
