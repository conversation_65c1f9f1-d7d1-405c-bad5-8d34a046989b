package com.metathought.food_order.casheir.network

import com.alibaba.fastjson.JSON
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.data.model.base.BaseBooleanResponse
import com.metathought.food_order.casheir.data.model.base.BaseResponse
import com.metathought.food_order.casheir.data.model.base.request_model.AddOrderMoreGoodRequest
import com.metathought.food_order.casheir.data.model.base.request_model.CheckVersionRequest
import com.metathought.food_order.casheir.data.model.base.request_model.CustomerInfoVo
import com.metathought.food_order.casheir.data.model.base.request_model.EditDeliveryOrderNoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.LogRequest
import com.metathought.food_order.casheir.data.model.base.request_model.PaymentRequest
import com.metathought.food_order.casheir.data.model.base.request_model.PendingRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.UploadLogRequest
import com.metathought.food_order.casheir.data.model.base.request_model.UserLoginRequest
import com.metathought.food_order.casheir.data.model.base.request_model.export.SalesOrdersExportRequest
import com.metathought.food_order.casheir.data.model.base.request_model.logout.CashierLogoutRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.CreateMemberRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.CreditListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.MemberListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.RechargeRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.TopUpCanUseCouponRequest
import com.metathought.food_order.casheir.data.model.base.request_model.member.UpdateMemberRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.AddTmpGoodsToCartRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.AsyncChangeRequest
import com.metathought.food_order.casheir.data.model.base.request_model.menu.CreateTmpGoodRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.AbNormalOrderV2Request
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.AcceptOrderInfoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.AcceptOrderListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.AnitSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CancelAcceptOrderRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CancelCreditRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CancelOrderRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CartConfirmPendingGoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CartGoodEditRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.CouponCodeRecongnitionRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.DeleteGoodsNumRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.DiscountSingleParamRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.OrderDiscountInfoListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.OrderListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PartialRefundRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PayAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PreSettlementRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.PrinterAgainRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.ReduceDiscountDetailRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.RepaymentRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateNoteRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateCustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateOrderOrGoodNoteRequest
import com.metathought.food_order.casheir.data.model.base.request_model.pending.PendingOrderListRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.AddToCartRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.ConfirmPendingGoodsRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.CreateTempTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.CustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.OrderToChangeTableTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.tableServiceRequest.SwitchTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLog
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.SaveOrUpdateOpeningCashRequest
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.ShiftReportPrint
import com.metathought.food_order.casheir.data.model.base.response_model.cart.CartInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.cart.OrderMoreDataResponse
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.RechargeTierPageResponse
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.home.StoreStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.dashboard.order_list.StoreOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.LoginUserInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.PermissionResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.StoreInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.login.UserLoginResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.ConsumerRechargeInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditInfo
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecordResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.MemberListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.PaymentStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeDetailResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RepaymentRecordResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.BalanceListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.statistic.MemberStatisticResponse
import com.metathought.food_order.casheir.data.model.base.response_model.offline.OfflineChannelTotalModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodDetail
import com.metathought.food_order.casheir.data.model.base.response_model.order.ReserveGoodListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ChangeGoodResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.DiscountReduceInfo
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.GoodClassificationModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderListRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MergeOrderRequest
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.MultipleOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.NoticeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTranslateResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.PaymentChannelModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.QuickRemarkModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ReduceDiscountDetailModel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.RepaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.TableOrderStatus
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.UnReadAndPrintResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.UncompletedOrderCountResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ab_normal.AbNormalOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.pending.PendingRecord
import com.metathought.food_order.casheir.data.model.base.response_model.pending.SerialNumberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrinterConfigInfo
import com.metathought.food_order.casheir.data.model.base.response_model.report.PaymentMethodReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.ProductReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.SaleReportResponse
import com.metathought.food_order.casheir.data.model.base.response_model.report.UnreadCashierMsgResponse
import com.metathought.food_order.casheir.data.model.base.response_model.store.StoreInfoMultLanguageListRespnose
import com.metathought.food_order.casheir.data.model.base.response_model.store.StoreInfoMultLanguageRespnose
import com.metathought.food_order.casheir.data.model.base.response_model.table.CreateTempTableCodeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.SwitchTableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponse
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.data.model.base.response_model.upload.UploadTokenResponse
import com.metathought.food_order.casheir.data.model.base.response_model.version.VersionCheckResponse
import com.metathought.food_order.casheir.database.ShoppingRecord
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.extension.toRequestBody
import com.metathought.food_order.casheir.network.service.ApiService
import retrofit2.http.Query
import timber.log.Timber
import java.math.BigDecimal
import javax.inject.Inject

class Repository
@Inject
constructor(private val apiService: ApiService) : BaseRepository() {
    suspend fun login(
        userModel: UserLoginRequest
    ): ApiResponse<UserLoginResponse> {
        return safeApiCall(
            call = { requestLogin(userModel) },
            errorMessage = ""
        )
    }

    private suspend fun requestLogin(userModel: UserLoginRequest): ApiResponse<UserLoginResponse> {
        val response = apiService.postLogin(userModel.toRequestBody())
        return executeResponse(response, {
            response.data.userAccount = userModel.username ?: ""
        })
    }

    suspend fun getPermissions(storeUserId: String): ApiResponse<PermissionResponse> {
        return safeApiCall(
            call = { requestPermissions(storeUserId) },
            errorMessage = ""
        )
    }

    private suspend fun requestPermissions(storeUserId: String): ApiResponse<PermissionResponse> {
        val response = apiService.getPermissions(storeUserId)
        return executeResponse(response)
    }

    /**
     * 查询这个手机号是否是店内的会员
     * Check whether this mobile phone number is a member of the store
     */
    suspend fun consumerPayAccount(
        telephone: String
    ): ApiResponse<CustomerMemberResponse?> {
        return safeApiCall(
            call = { requestConsumerPayAccount(telephone) },
            errorMessage = ""
        )
    }

    private suspend fun requestConsumerPayAccount(telephone: String): ApiResponse<CustomerMemberResponse> {
        val response = apiService.consumerPayAccount(telephone)
        return executeResponse(response)
    }

    suspend fun electFreelist(
//        plan: Int = 0,
        diningStyle: Int,
        shoppingRecord: ShoppingRecord?,
    ): ApiResponse<TableResponse> {
        return safeApiCall(
//            call = { requestElectFreelist(plan, diningStyle, shoppingRecord) },
            call = { requestElectFreelist(diningStyle, shoppingRecord) },
            errorMessage = ""
        )
    }

    private suspend fun requestElectFreelist(
//        plan: Int = 0,
        diningStyle: Int,
        shoppingRecord: ShoppingRecord?,
    ): ApiResponse<TableResponse> {
//        val response = apiService.electFreelist(plan, diningStyle)
        val response = apiService.electFreelist(diningStyle)
        return executeResponse(response, {
            if (response.data.size > 0 && !shoppingRecord?.tableUuid.isNullOrEmpty()) {
                response.data.firstOrNull { it.uuid == shoppingRecord?.tableUuid }?.select =
                    true
            }
        })
    }


    suspend fun getTable(keyword: String): ApiResponse<TableResponse> {
        return safeApiCall(
            call = { requestGetTable(keyword) },
            errorMessage = ""
        )
//        return safeApiCall(call = {
//            try {
//                val response = apiService.getTable(keyword)
//                if (response.isSuccess()) {
//                    ApiResponse.Success(response.data)
//                } else {
//                    ApiResponse.Error(response.msg, response.code)
//                }
//            } catch (e: SSLHandshakeException) {
//                e.printStackTrace()
//                ApiResponse.Error("", 1)
//            } catch (e: Exception) {
//                e.printStackTrace()
//                ApiResponse.Error(e.message, 1)
//            }
//        }, errorMessage = "")
    }

    private suspend fun requestGetTable(keyword: String): ApiResponse<TableResponse> {
        val response = apiService.getTable(keyword)
        return executeResponse(response)
//        return safeApiCall(call = {
//            try {
//                val response = apiService.getTable(keyword)
//                if (response.isSuccess()) {
//                    ApiResponse.Success(response.data)
//                } else {
//                    ApiResponse.Error(response.msg, response.code)
//                }
//            } catch (e: SSLHandshakeException) {
//                e.printStackTrace()
//                ApiResponse.Error("", 1)
//            } catch (e: Exception) {
//                e.printStackTrace()
//                ApiResponse.Error(e.message, 1)
//            }
//        }, errorMessage = "")
    }

    suspend fun reserveTable(tableRequest: ReserveTableRequest): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestReserveTable(tableRequest) },
            errorMessage = ""
        )
//        return try {
//            val response = apiService.seatTable(tableRequest.toRequestBody())
//            if (response.isSuccess()) {
//                ApiResponse.Success(response)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    private suspend fun requestReserveTable(tableRequest: ReserveTableRequest): ApiResponse<BaseBooleanResponse> {
        val response = apiService.seatTable(tableRequest.toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
//        return try {
//            val response = apiService.seatTable(tableRequest.toRequestBody())
//            if (response.isSuccess()) {
//                ApiResponse.Success(response)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    suspend fun cancelReserveTable(tableId: String): ApiResponse<TableResponseItem> {
        return safeApiCall(call = { requestCancelReserveTable(tableId) }, errorMessage = "")
    }

    private suspend fun requestCancelReserveTable(tableId: String): ApiResponse<TableResponseItem> {
        val response = apiService.freeTable(tableID = tableId)
        return executeResponse(response)
    }

//    suspend fun getGoodsReserveList(
//        goodsName: String,
//        lastChangeData: String
//    ): ApiResponse<ReserveGoodListResponse> {
//        return try {
//            val response = apiService.getGoodsReserveList(goodsName, lastChangeData)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
//    }

//    suspend fun getGoodsList(
//        diningStyle: String,
//        goodName: String,
//        lastChangeData: String
//    ): ApiResponse<ReserveGoodListResponse> {
//        return try {
//            val response = apiService.getGoodsList(diningStyle, goodName, lastChangeData)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
//    }

    suspend fun getGoodsListV2(
        diningStyle: String,
        goodName: String,
        lastChangeData: String
    ): ApiResponse<ReserveGoodListResponse> {
        return safeApiCall(
            call = { requestGetGoodsListV2(diningStyle, goodName, lastChangeData) },
            errorMessage = ""
        )
//        return try {
//            val response = apiService.getGoodsListV2(diningStyle, goodName, lastChangeData)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    private suspend fun requestGetGoodsListV2(
        diningStyle: String,
        goodName: String,
        lastChangeData: String
    ): ApiResponse<ReserveGoodListResponse> {
        val response =
            apiService.getGoodsListV2(diningStyle, goodName, lastChangeData)
        return executeResponse(response)
//        return try {
//            val response = apiService.getGoodsListV2(diningStyle, goodName, lastChangeData)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    suspend fun getTakeOutGoodsList(
        takeOutPlatformId: String,
        goodName: String,
        lastUpdateDate: String
    ): ApiResponse<ReserveGoodListResponse> {
        return safeApiCall(
            call = { requestGetTakeOutGoodsList(takeOutPlatformId, goodName, lastUpdateDate) },
            errorMessage = ""
        )
//        return try {
//            val response =
//                apiService.getDeliveryPlatformGoodList(takeOutPlatformId, goodName, lastUpdateDate)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    private suspend fun requestGetTakeOutGoodsList(
        takeOutPlatformId: String,
        goodName: String,
        lastUpdateDate: String
    ): ApiResponse<ReserveGoodListResponse> {
        val response =
            apiService.getDeliveryPlatformGoodList(takeOutPlatformId, goodName, lastUpdateDate)
        return executeResponse(response)
//        return try {
//            val response =
//                apiService.getDeliveryPlatformGoodList(takeOutPlatformId, goodName, lastUpdateDate)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }


    suspend fun getGoodsReserveDetail(goodsID: String): ApiResponse<ReserveGoodDetail> {
        return safeApiCall(
            call = { requestGetGoodsReserveDetail(goodsID) },
            errorMessage = ""
        )
//        return try {
//            val response = apiService.getReserveGoodsDetail(goodsID)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    private suspend fun requestGetGoodsReserveDetail(goodsID: String): ApiResponse<ReserveGoodDetail> {
//        return try {
//            val response = apiService.getReserveGoodsDetail(goodsID)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
        val response = apiService.getReserveGoodsDetail(goodsID)
        return executeResponse(response)
    }


    suspend fun getGoodsDetail(goodsID: String): ApiResponse<ReserveGoodDetail> {
        return safeApiCall(
            call = { requestGetGoodsDetail(goodsID) },
            errorMessage = ""
        )
//        return try {
//            val response = apiService.getGoodsDetail(goodsID)
//            if (response.isSuccess()) {
//                ApiResponse.Success(response.data)
//            } else {
//                ApiResponse.Error(response.msg, response.code)
//            }
//        } catch (e: SSLHandshakeException) {
//            e.printStackTrace()
//            ApiResponse.Error("", 1)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            ApiResponse.Error(e.message, 1)
//        }
    }

    private suspend fun requestGetGoodsDetail(goodsID: String): ApiResponse<ReserveGoodDetail> {
        val response = apiService.getGoodsDetail(goodsID)
        return executeResponse(response)
    }


    /**
     * Order -> Dinner - In | Take Away | After Pay - > Confirmation
     */
    suspend fun cartCreateOrder(
        paymentRequest: PaymentRequest,
        payType: Int
    ): ApiResponse<PaymentResponse> {
        return safeApiCall(
            call = { requestCartCreateOrder(paymentRequest, payType) },
            errorMessage = ""
        )
    }

    private suspend fun requestCartCreateOrder(
        paymentRequest: PaymentRequest,
        payType: Int
    ): ApiResponse<PaymentResponse> {
        val response = apiService.cartCreateOrder(paymentRequest.toRequestBody())
        return executeResponse(response, {
            response.data.apply {
                this.payType = payType

                //如果qrcode 未null 说明是 0元购 qrcode 返回为null  支付类型改为现金支付
                if (payType == PayTypeEnum.ONLINE_PAYMENT.id) {
                    if (qrcode == null) {
                        this.payType = PayTypeEnum.CASH_PAYMENT.id
                    }
                }
            }
        })
    }


    /**
     * Ordered-> List
     */
    suspend fun orderedList(orderListRequest: OrderListRequest): ApiResponse<OrderedTotalResponse> {
        return safeApiCall(
            call = { requestOrderedList(orderListRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderedList(orderListRequest: OrderListRequest): ApiResponse<OrderedTotalResponse> {
        val response = apiService.orderList(orderListRequest.toRequestBody())
        return executeResponse(response)
    }

    /**
     * Ordered-> List  V2
     */
    suspend fun orderListV2(orderListRequest: OrderListRequest): ApiResponse<OrderedTotalResponse> {
        return safeApiCall(
            call = { requestOrderListV2(orderListRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderListV2(orderListRequest: OrderListRequest): ApiResponse<OrderedTotalResponse> {
        val response = apiService.orderListV2(orderListRequest.toRequestBody())
        return executeResponse(response)
    }


    /**
     * Ordered - > Info
     */
    suspend fun orderedInfo(
        orderNo: String?,
        isRead: Boolean? = false
    ): ApiResponse<OrderedInfoResponse> {
        return safeApiCall(
            call = { requestOrderedInfo(orderNo, isRead) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderedInfo(
        orderNo: String?,
        isRead: Boolean? = false
    ): ApiResponse<OrderedInfoResponse> {
        val response = apiService.orderInfo(orderNo, isRead)
        return executeResponse(response)
    }


    /**
     * Ordered -> Pay Again
     */
    suspend fun orderedPayAgain(
        payAgainRequest: PayAgainRequest,
        payType: Int
    ): ApiResponse<PaymentResponse> {
        return safeApiCall(
            call = { requestOrderedPayAgain(payAgainRequest, payType) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderedPayAgain(
        payAgainRequest: PayAgainRequest,
        payType: Int
    ): ApiResponse<PaymentResponse> {
        val response = apiService.againPay(payAgainRequest.toRequestBody())
        return executeResponse(response, {
            if (response.data != null) {
                response.data.payType = payType
                //如果qrcode 未null 说明是 0元购 qrcode 返回为null  支付类型改为现金支付
                if (response.data.qrcode == null && response.data.payType != PayTypeEnum.CREDIT.id) {
                    response.data.payType = PayTypeEnum.CASH_PAYMENT.id
                }
                if (response.data.orderNo == null) {
                    response.data.orderNo = payAgainRequest.orderNo
                }
            }
        })
    }

    /**
     * Ordered -> get Table list filter
     */
    suspend fun orderedTableList(): ApiResponse<OrderedTableListResponse> {
        return safeApiCall(
            call = { requestOrderedTableList() },
            errorMessage = ""
        )
    }

    suspend fun requestOrderedTableList(): ApiResponse<OrderedTableListResponse> {
        val response = apiService.storeTable()
        return executeResponse(response)
    }

    /**
     * full refund
     */
    suspend fun fullRefund(
        orderId: String?,
        payType: Int,
        autoInStock: Boolean
    ): ApiResponse<PaymentResponse> {
        return safeApiCall(
            call = { requestFullRefund(orderId, payType, autoInStock) },
            errorMessage = ""
        )
    }

    private suspend fun requestFullRefund(
        orderId: String?,
        payType: Int,
        autoInStock: Boolean
    ): ApiResponse<PaymentResponse> {
        val response = apiService.fullRefund(orderId, payType, 3, autoInStock = autoInStock)
        return if (response.isSuccess()) {
            ApiResponse.Success(
                PaymentResponse(
                    orderNo = orderId,
                    payType = payType
                )
            )
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    /**
     * full refund
     */
    suspend fun partialRefund(
        partialRefundRequest: PartialRefundRequest
    ): ApiResponse<PaymentResponse> {
        return safeApiCall(
            call = { requestPartialRefund(partialRefundRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestPartialRefund(
        partialRefundRequest: PartialRefundRequest
    ): ApiResponse<PaymentResponse> {
        val response = apiService.partRefund(partialRefundRequest.toRequestBody())
//        return executeResponse(response)
        return if (response.isSuccess()) {
            ApiResponse.Success(
                PaymentResponse(
                    orderNo = partialRefundRequest.orderId,
                    payType = partialRefundRequest.payType
                )
            )
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    /**
     * Pending Order - Add
     */
    suspend fun addPendingOrder(
        pendingRequest: PendingRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestPendingOrder(pendingRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestPendingOrder(
        pendingRequest: PendingRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.addPendingOrder(pendingRequest.toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }

    suspend fun getPendingList(pendingOrderListRequest: PendingOrderListRequest): ApiResponse<PendingListResponse> {
        return safeApiCall(
            call = { requestPendingOrderList(pendingOrderListRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestPendingOrderList(pendingOrderListRequest: PendingOrderListRequest): ApiResponse<PendingListResponse> {
        val response = apiService.getPendingOrderList(pendingOrderListRequest.toRequestBody())
        return executeResponse(response)
    }

    suspend fun deletedPendingOrder(
        orderNo: String?
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestDeletePendingOrder(orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestDeletePendingOrder(
        orderNo: String?
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.deletePendingOrder(orderNo)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun getPendingOrder(
        orderNo: String?
    ): ApiResponse<PendingRecord> {
        return safeApiCall(
            call = { requestGetPendingOrder(orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetPendingOrder(
        orderNo: String?
    ): ApiResponse<PendingRecord> {
        val response = apiService.getPendingOrder(orderNo)
        return if (response.isSuccess()) {
            ApiResponse.Success(response.data)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun getPrintTemplate(): ApiResponse<PrintTamplateResponse> {
        return safeApiCall(
            call = { requestPrintTemplate() },
            errorMessage = ""
        )
    }

    private suspend fun requestPrintTemplate(): ApiResponse<PrintTamplateResponse> {
        val response = apiService.getPrinterTemplate()
        return executeResponse(response)
    }

    suspend fun addToCart(
        addToCartRequest: AddToCartRequest
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = { requestAddToCart(addToCartRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestAddToCart(
        addToCartRequest: AddToCartRequest
    ): ApiResponse<CartInfoResponse> {
        val response = apiService.addToCart(addToCartRequest.toRequestBody())
        return executeResponse(response)
//        return if (response.isSuccess()) {
//            ApiResponse.Success(response)
//        } else {
//            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
//        }
    }

    suspend fun changeNum(
        addToCartRequest: AddToCartRequest
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = { requestChangeNum(addToCartRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestChangeNum(
        addToCartRequest: AddToCartRequest
    ): ApiResponse<CartInfoResponse> {
        val response = apiService.changeNum(addToCartRequest.toRequestBody())
//        return if (response.isSuccess()) {
//            ApiResponse.Success(response)
//        } else {
//            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
//        }
        return executeResponse(response)
    }

    suspend fun deductFromCart(
        diningStyle: Int?,
        tableUuid: String?,
        goodsID: String?,
        cartId: String?,
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = { requestDeductFromCart(diningStyle, tableUuid, goodsID, cartId) },
            errorMessage = ""
        )
    }

    private suspend fun requestDeductFromCart(
        diningStyle: Int?,
        tableUuid: String?,
        goodsID: String?,
        cartId: String?,
    ): ApiResponse<CartInfoResponse> {
        val response = apiService.reduceFromCart(
            diningStyle = diningStyle,
            tableUuid = tableUuid,
            goodsId = goodsID,
            cartId = cartId,
        )
        return executeResponse(response)
    }

    suspend fun emptyFromCart(
        diningStyle: Int?, tableUuid: String?
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestEmptyCart(diningStyle, tableUuid) },
            errorMessage = ""
        )
    }

    private suspend fun requestEmptyCart(
        diningStyle: Int?, tableUuid: String?
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.emptyCart(diningStyle = diningStyle, tableUuid = tableUuid)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }

    suspend fun getCartInfo(diningStyle: Int?, tableUuid: String?): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = { requestCartInfo(diningStyle, tableUuid) },
            errorMessage = ""
        )
    }

    private suspend fun requestCartInfo(
        diningStyle: Int?,
        tableUuid: String?
    ): ApiResponse<CartInfoResponse> {
        val response = apiService.getCartInfo(diningStyle, tableUuid)
        return executeResponse(response)
    }

    suspend fun switchTable(switchTableRequest: SwitchTableRequest): ApiResponse<SwitchTableResponse> {
        return safeApiCall(
            call = { requestSwitchTable(switchTableRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestSwitchTable(switchTableRequest: SwitchTableRequest): ApiResponse<SwitchTableResponse> {
        val response = apiService.switchTable(switchTableRequest.toRequestBody())
        return executeResponse(response)
    }

    suspend fun addMoreGoods(
        requestBody: AddOrderMoreGoodRequest
    ): ApiResponse<OrderedInfoResponse> {
        return safeApiCall(
            call = { requestAddMoreGoods(requestBody) },
            errorMessage = ""
        )
    }

    private suspend fun requestAddMoreGoods(
        requestBody: AddOrderMoreGoodRequest
    ): ApiResponse<OrderedInfoResponse> {
        val response = apiService.addMoreGood(requestBody.toRequestBody())
        return executeResponse(response)
    }

    suspend fun getOrderMoreProduct(
        diningStyle: Int?,
        tableUuid: String?,
        orderId: String? = null
    ): ApiResponse<OrderMoreDataResponse> {
        return safeApiCall(
            call = { requestOrderMoreProduct(diningStyle, tableUuid, orderId) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderMoreProduct(
        diningStyle: Int?,
        tableUuid: String?,
        orderId: String? = null
    ): ApiResponse<OrderMoreDataResponse> {
        val response = apiService.getOrderMore(
            diningStyle,
            tableUuid,
            if (orderId.isNullOrEmpty()) null else orderId
        )
        return executeResponse(response)
    }

    suspend fun getMemberList(
        pageSize: Int?,
        page: Int?,
        upayAccount: String?,
        userType: String?,
        startTime: String?,
        endTime: String?,
    ):
            ApiResponse<MemberListResponse> {
        return safeApiCall(
            call = { requestMemberList(pageSize, page, upayAccount, userType, startTime, endTime) },
            errorMessage = ""
        )
    }

    private suspend fun requestMemberList(
        pageSize: Int?,
        page: Int?,
        upayAccount: String?,
        userType: String?,
        startTime: String?,
        endTime: String?,
    )
            : ApiResponse<MemberListResponse> {
        val response =
            apiService.getMemberList(
                MemberListRequest(
                    pageSize,
                    page,
                    upayAccount,
                    userType,
                    startTime,
                    endTime
                ).toRequestBody()
            )
        return executeResponse(response)
    }

    //会员充值金额-详情页
    suspend fun getRechargeDetailPage(
        consumerPayAccountId: String?,
    ): ApiResponse<RechargeDetailResponse> {
        return safeApiCall(
            call = { requestRechargeDetailPage(consumerPayAccountId) },
            errorMessage = ""
        )
    }

    private suspend fun requestRechargeDetailPage(
        consumerPayAccountId: String?,
    ): ApiResponse<RechargeDetailResponse> {
        val response =
            apiService.getRechargeDetailPage(consumerPayAccountId)
        return executeResponse(response)
    }

    //获取充值赠送优惠券列表(总店适用门店全部、包含该门店、当前店铺非结束优惠券)
    suspend fun getRechargeTierPage(
        page: Int?,  //页码
        pageSize: Int?,  //数量
        name: String?,   //优惠券模板名称
        type: String?,   //优惠券分类
        activeState: String?, //状态 NOT_STARTED未开始 IN_PROGRESS进行中
        available: Boolean?,    //是否启用，不传查全部启用状态
    ): ApiResponse<RechargeTierPageResponse> {
        return safeApiCall(
            call = { requestRechargeTierPage(page, pageSize, name, type, activeState, available) },
            errorMessage = ""
        )
    }

    private suspend fun requestRechargeTierPage(
        page: Int?,  //页码
        pageSize: Int?,  //数量
        name: String?,   //优惠券模板名称
        type: String?,   //优惠券分类
        activeState: String?, //状态 NOT_STARTED未开始 IN_PROGRESS进行中
        available: Boolean?,    //是否启用，不传查全部启用状态
    ): ApiResponse<RechargeTierPageResponse> {
        val response =
            apiService.getRechargeTierPage(page, pageSize, name, type, activeState, available)
        return executeResponse(response)
    }

    suspend fun getMemberBalanceList(
        pageSize: Int?,
        page: Int?,
        upayAccount: String?,
        type: String?,
        startTime: String?,
        endTime: String?,
        exactMatch: Boolean? = false
    ):
            ApiResponse<BalanceListResponse> {
        return safeApiCall(
            call = {
                requestMemberBalanceList(
                    pageSize,
                    page,
                    upayAccount,
                    type,
                    startTime,
                    endTime,
                    exactMatch
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestMemberBalanceList(
        pageSize: Int?,
        page: Int?,
        upayAccount: String?,
        type: String?,
        startTime: String?,
        endTime: String?,
        exactMatch: Boolean? = false
    )
            : ApiResponse<BalanceListResponse> {
        val response =
            apiService.getMemberBalanceList(
                pageSize,
                page,
                upayAccount,
                type,
                startTime,
                endTime,
                exactMatch
            )
        return executeResponse(response)
    }

    //获取挂账列表
    suspend fun getCreditList(
        keyword: String?,//关键字搜索:手机号、昵称
        page: Int,
        pageSize: Int,
    ): ApiResponse<CreditListResponse> {
        return safeApiCall(
            call = {
                requestCreditList(keyword, page, pageSize)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCreditList(
        keyword: String?,//关键字搜索:手机号、昵称
        page: Int,
        pageSize: Int,
    ): ApiResponse<CreditListResponse> {
        val response =
            apiService.getCreditList(
                CreditListRequest(keyword, page, pageSize).toRequestBody()
            )
        return executeResponse(response)
    }

    //获取挂账信息
    suspend fun getCreditInfo(
        consumerId: Long?
    ): ApiResponse<CreditInfo> {
        return safeApiCall(
            call = {
                requestCreditInfo(consumerId)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCreditInfo(consumerId: Long?): ApiResponse<CreditInfo> {
        val response =
            apiService.getCreditInfo(consumerId)
        return executeResponse(response)
    }

    //挂账记录列表
    suspend fun getCreditRecordList(
        consumerId: Long,
        page: Int?,
        pageSize: Int?
    ): ApiResponse<CreditRecordResponse> {
        return safeApiCall(
            call = {
                requestCreditRecordList(consumerId, page, pageSize)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCreditRecordList(
        consumerId: Long,
        page: Int?,
        pageSize: Int?
    ): ApiResponse<CreditRecordResponse> {
        val response =
            apiService.getCreditRecordList(
                consumerId, page, pageSize
            )
        return executeResponse(response)
    }

    //还款记录列表
    suspend fun getRepaymentRecordList(
        consumerId: Long,
        page: Int?,
        pageSize: Int?
    ): ApiResponse<RepaymentRecordResponse> {
        return safeApiCall(
            call = {
                requestRepaymentRecordList(consumerId, page, pageSize)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestRepaymentRecordList(
        consumerId: Long,
        page: Int?,
        pageSize: Int?
    ): ApiResponse<RepaymentRecordResponse> {
        val response =
            apiService.getRepaymentRecordList(
                consumerId, page, pageSize
            )
        return executeResponse(response)
    }

    //余额明细详情
    suspend fun getOrderDetailInfo(id: String?): ApiResponse<ConsumerRechargeInfo> {
        return safeApiCall(
            call = {
                requestOrderDetailInfo(id)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderDetailInfo(id: String?)
            : ApiResponse<ConsumerRechargeInfo> {
        val response = apiService.getOrderDetailInfo(id)
        return executeResponse(response)
    }

    suspend fun rechargeBalance(rechargeRequest: RechargeRequest): ApiResponse<RechargeResponse> {
        return safeApiCall(
            call = { requestRechargeBalance(rechargeRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestRechargeBalance(rechargeRequest: RechargeRequest): ApiResponse<RechargeResponse> {
        val response = apiService.rechargeBalance(rechargeRequest.toRequestBody())
        return if (response.isSuccess()) {
            response.data?.apply {
                payType = rechargeRequest.type ?: 0
            }
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun getStoreStatistic(
        pageSize: Int?,
        page: Int?,
        type: String?,
        startTime: String?,
        endTime: String?,
    ):
            ApiResponse<StoreStatisticResponse> {
        return safeApiCall(
            call = {
                requestStoreStatistic(
                    pageSize, page, if (type == "") {
                        null
                    } else {
                        type
                    }, startTime, endTime
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestStoreStatistic(
        pageSize: Int?,
        page: Int?,
        type: String?,
        startTime: String?,
        endTime: String?,
    )
            : ApiResponse<StoreStatisticResponse> {
        val response =
            apiService.getDashBoard(pageSize, page, type, startTime, endTime)
        return executeResponse(response)
    }


    suspend fun getStoreOrdered(
        pageSize: Int?,
        page: Int?,
        startTime: String?,
        endTime: String?,
        paymentMethod: String?,
        orderStatus: String?,
//        type: String?,
        keyword: String?,
        channelsId: Int?
    ):
            ApiResponse<StoreOrderListResponse> {
        return safeApiCall(
            call = {
                requestStoreOrdered(
                    pageSize,
                    page,
                    startTime,
                    endTime,
                    paymentMethod,
                    orderStatus,
//                    type,
                    keyword,
                    channelsId
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestStoreOrdered(
        pageSize: Int?,
        page: Int?,
        startTime: String?,
        endTime: String?,
        paymentMethod: String?,
        orderStatus: String?,
//        type: String?,
        keyword: String?,
        channelsId: Int?,
    )
            : ApiResponse<StoreOrderListResponse> {
        val response =
            apiService.getStoreOrder(
                pageSize,
                page,
                startTime,
                endTime,
                paymentMethod,
                orderStatus,
//                type,
                keyword, channelsId
            )
        return executeResponse(response)
    }

    suspend fun getMemberStatistic(
        type: String?,
        startTime: String?,
        endTime: String?,
    ):
            ApiResponse<MemberStatisticResponse> {
        return safeApiCall(
            call = { requestMemberStatistic(type, startTime, endTime) },
            errorMessage = ""
        )
    }

    private suspend fun requestMemberStatistic(
        type: String?,
        startTime: String?,
        endTime: String?,
    )
            : ApiResponse<MemberStatisticResponse> {
        val response =
            apiService.getMemberStatistic(type, startTime, endTime)
        return executeResponse(response)
    }

    suspend fun getMultiplePrint(orderID: String?):
            ApiResponse<MultipleOrderResponse> {
        return safeApiCall(
            call = { requestMultiPrint(orderID) },
            errorMessage = ""
        )
    }

    private suspend fun requestMultiPrint(orderID: String?)
            : ApiResponse<MultipleOrderResponse> {
        val response =
            apiService.getMultiplePrinter(orderID)
        return executeResponse(response)
    }

    suspend fun getConsumer(keyword: String?):
            ApiResponse<List<ConsumerResponse>> {
        return safeApiCall(
            call = { requestConsumer(keyword) },
            errorMessage = ""
        )
    }

    private suspend fun requestConsumer(keyword: String?)
            : ApiResponse<List<ConsumerResponse>> {
        val response =
            apiService.getConsumer(keyword)
        return executeResponse(response)
    }

    suspend fun postPrintTicket(requestObject: PrinterAgainRequest):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestPrintAgain(requestObject) },
            errorMessage = ""
        )
    }

    private suspend fun requestPrintAgain(requestObject: PrinterAgainRequest)
            : ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.postPrinterAgain(requestObject.toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }

    suspend fun putLogout(requestObject: CashierLogoutRequest):
            ApiResponse<ShiftReportPrint> {
        return safeApiCall(
            call = { requestLogout(requestObject) },
            errorMessage = ""
        )
    }

    private suspend fun requestLogout(requestObject: CashierLogoutRequest)
            : ApiResponse<ShiftReportPrint> {
        val response =
            apiService.putLogout(requestObject.toRequestBody())
        return executeResponse(response)
//        return if (response.isSuccess()) {
//            ApiResponse.Success(response)
//        } else {
//            ApiResponse.Error(response.msg, response.code)
//        }
    }

    suspend fun getLoginUserInfo():
            ApiResponse<LoginUserInfoResponse> {
        return safeApiCall(
            call = { requestLoginUserInfo() },
            errorMessage = ""
        )
    }

    private suspend fun requestLoginUserInfo()
            : ApiResponse<LoginUserInfoResponse> {
        val response =
            apiService.getLoginUserInfo()
        return executeResponse(response)
    }

    suspend fun getAbNormalOrder(tableUuid: String?):
            ApiResponse<AbNormalOrderResponse> {
        return safeApiCall(
            //currently we use only 6 for payStatus in cashier system (1-unPaid 6-confirmed)
            call = { requestAbNormalOrder(tableUuid, 6) },
            errorMessage = ""
        )
    }

    private suspend fun requestAbNormalOrder(tableUuid: String?, payStatus: Int?)
            : ApiResponse<AbNormalOrderResponse> {
        val response =
            apiService.getAbNormalOrder(tableUuid, payStatus)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg?.toString(), response.code)
        }
    }

    suspend fun getRechargeQRStatus(orderNo: String?): ApiResponse<RechargeQRStatusResponse> {
        return safeApiCall(
            call = { requestRechargeQRStatus(orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestRechargeQRStatus(
        orderNo: String?,
    ): ApiResponse<RechargeQRStatusResponse> {
        val response = apiService.rechargeQRStatus(orderNo)
        return executeResponse(response)
    }

    suspend fun getPaymentStatusPolling(outTradeNo: String?): ApiResponse<PaymentStatusResponse> {
        return safeApiCall(
            call = { requestPaymentStatus(outTradeNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestPaymentStatus(
        outTradeNo: String?
    ): ApiResponse<PaymentStatusResponse> {
        val response = apiService.getPaymentStatusPolling(outTradeNo)
        return executeResponse(response)
    }

    suspend fun getSerialNumber(): ApiResponse<SerialNumberResponse> {
        return safeApiCall(
            call = { requestSerialNumber() },
            errorMessage = ""
        )
    }

    private suspend fun requestSerialNumber(): ApiResponse<SerialNumberResponse> {
        val response = apiService.getSerialNumber()
        return executeResponse(response)
    }

    suspend fun putCancelTopUp(topUpID: String):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestCancelTopUp(topUpID) },
            errorMessage = ""
        )
    }

    private suspend fun requestCancelTopUp(topUpID: String)
            : ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.putCancelTopUp(topUpID)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    /**
     * 获取线下支付渠道列表
     * Get a list of offline payment channels
     */
    suspend fun getOfflineChannels(): ApiResponse<OfflineChannelTotalModel> {
        return safeApiCall(
            call = { requestGetOfflineChannels() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetOfflineChannels(): ApiResponse<OfflineChannelTotalModel> {
        val response = apiService.getOfflineChannels()
        return executeResponse(response, {
//            if (response.data.isNotEmpty()) {
//                response.data.first().select = true
//            }
        })
    }


    /**
     * 取消订单
     * Cancel Order
     */
    suspend fun cancelOrder(
        orderId: String,
        cancelReason: String?,
        autoInStock: Boolean
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestCancelOrder(orderId, cancelReason, autoInStock) },
            errorMessage = ""
        )
    }

    private suspend fun requestCancelOrder(
        orderId: String,
        cancelReason: String?,
        autoInStock: Boolean
    )
            : ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.cancelOrder(
                CancelOrderRequest(
                    orderId,
                    cancelReason,
                    autoInStock
                ).toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    /**
     * 取消挂账
     * Cancel Order
     */
    suspend fun cancelCredit(
        orderId: String,
        cancelReason: String?,
        autoInStock: Boolean
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestCancelCredit(orderId, cancelReason, autoInStock) },
            errorMessage = ""
        )
    }

    private suspend fun requestCancelCredit(
        orderId: String,
        cancelReason: String?,
        autoInStock: Boolean
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.cancelCredit(
            CancelCreditRequest(
                orderId,
                cancelReason,
                autoInStock
            ).toRequestBody()
        )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    /**
     * 还款
     */
    suspend fun repayment(
        repaymentRequest: RepaymentRequest,
    ): ApiResponse<RepaymentResponse> {
        return safeApiCall(
            call = { requestRepayment(repaymentRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestRepayment(
        repaymentRequest: RepaymentRequest,
    ): ApiResponse<RepaymentResponse> {
        val response = apiService.repayment(
            repaymentRequest.toRequestBody()
        )
        return executeResponse(response)
    }


    /**
     * 退菜
     * Cancel Order
     */
    suspend fun changeGood(deleteGoodsNumRequest: DeleteGoodsNumRequest): ApiResponse<ChangeGoodResponse> {
        return safeApiCall(
            call = { requestChangeGood(deleteGoodsNumRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestChangeGood(deleteGoodsNumRequest: DeleteGoodsNumRequest)
            : ApiResponse<ChangeGoodResponse> {
        val response =
            apiService.changeGood(deleteGoodsNumRequest.toRequestBody())
        return executeResponse(response)
    }

    /**
     * 创建临时桌码打印请求
     * Create a temporary table code print request
     */
    suspend fun createTempTableCode(model: CreateTempTableRequest): ApiResponse<CreateTempTableCodeResponse> {
        return safeApiCall(
            call = { requestCreateTempTableCode(model) },
            errorMessage = ""
        )
    }

    private suspend fun requestCreateTempTableCode(model: CreateTempTableRequest): ApiResponse<CreateTempTableCodeResponse> {
        val response = apiService.createTempTableCode(model.refTableId, model.isPosPrint)
        return executeResponse(response)
    }


    /**
     * 称重/定价使用新接口
     * Modify Product Weight
     */
    suspend fun confirmPendingGoods(model: ConfirmPendingGoodsRequest): ApiResponse<List<OrderedGoods>> {
        return safeApiCall(
            call = { requestConfirmPendingGoods(model) },
            errorMessage = ""
        )
    }

    private suspend fun requestConfirmPendingGoods(model: ConfirmPendingGoodsRequest): ApiResponse<List<OrderedGoods>> {
        val response = apiService.confirmPendingGoods(model.toRequestBody())
        return executeResponse(response)
    }


    /**
     * 餐桌是否有未完成订单
     *
     */
    suspend fun uncompletedOrderCount(
        srcTableUuid: String?,
        targetTableUuid: String?
    ): ApiResponse<List<TableOrderStatus>> {
        return safeApiCall(
            call = { requestUncompletedOrderCount(srcTableUuid, targetTableUuid) },
            errorMessage = ""
        )
    }

    private suspend fun requestUncompletedOrderCount(
        srcTableUuid: String?,
        targetTableUuid: String?
    ): ApiResponse<List<TableOrderStatus>> {
        val response = apiService.uncompletedOrderCount("${srcTableUuid},${targetTableUuid}")
        return executeResponse(response)
    }

    /**
     * 餐桌是否有未完成订单
     *
     */
    suspend fun uncompletedOrderCountV2(
        srcTableUuid: String?,
        targetTableUuid: String?,
        orderNo: String?
    ): ApiResponse<UncompletedOrderCountResponse> {
        return safeApiCall(
            call = { requestUncompletedOrderCountV2(srcTableUuid, targetTableUuid, orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestUncompletedOrderCountV2(
        srcTableUuid: String?,
        targetTableUuid: String?,
        orderNo: String?
    ): ApiResponse<UncompletedOrderCountResponse> {
        val response =
            apiService.uncompletedOrderCountV2("${srcTableUuid},${targetTableUuid}", orderNo)
        return executeResponse(response)
    }


    /**
     * 订单换桌
     *
     */
    suspend fun orderToChangeTable(
        allInUnpaid: Boolean,
        newTableUuid: String?,
        orderNo: String?,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestOrderToChangeTable(allInUnpaid, newTableUuid, orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderToChangeTable(
        allInUnpaid: Boolean,
        newTableUuid: String?,
        orderNo: String?,
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.orderToChangeTable(
            OrderToChangeTableTableRequest(
                allInUnpaid = allInUnpaid,
                newTableUuid = newTableUuid,
                orderNo = orderNo
            ).toRequestBody()
        )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }

    /**
     * 根据orderNo获取翻译
     */
    suspend fun orderTranslate(orderedInfoResponse: OrderedInfoResponse?): ApiResponse<OrderedTranslateResponse> {
        return safeApiCall(
            call = { requestOrderTranslate(orderedInfoResponse) },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderTranslate(orderedInfoResponse: OrderedInfoResponse?): ApiResponse<OrderedTranslateResponse> {
        val response = apiService.orderTranslate(
//            PreSettlementRequest(
//                orderedInfoResponse?.orderNo,
//                orderedInfoResponse?.coupon?.code,
////                if (orderedInfoResponse?.reduceDollar != null) BigDecimal(
////                    orderedInfoResponse.reduceDollar!!.div(
////                        100.0
////                    )
////                ).halfUp(2).stripTrailingZeros() else null,
////                if (orderedInfoResponse?.reduceKhr != null) orderedInfoResponse?.reduceKhr?.toBigDecimal() else null,
////                orderedInfoResponse?.reduceRate,
////                orderedInfoResponse?.reduceType
//                reduceType = orderedInfoResponse?.getWholeDiscountType(),
//                reduceRate = if (orderedInfoResponse?.getWholeDiscountType() == WholeDiscountType.PERCENTAGE.id) orderedInfoResponse?.price?.wholeDiscountReduce?.reduceRate else null,
//                reduceDollar = if (orderedInfoResponse?.getWholeDiscountType() == WholeDiscountType.FIXED_AMOUNT.id) orderedInfoResponse?.price?.wholeDiscountReduce?.reduceDollar else null,
//                reduceVipDollar = if (orderedInfoResponse?.getWholeDiscountType() == WholeDiscountType.FIXED_AMOUNT.id) orderedInfoResponse?.vipPrice?.wholeDiscountReduce?.reduceDollar else null,
//                discountReduceActivityId = orderedInfoResponse?.discountReduceActivity?.id
//
//            ).toRequestBody()
            PreSettlementRequest(
                orderNo = orderedInfoResponse?.orderNo,
                couponCode = orderedInfoResponse?.getCurrentCoupon()?.code,
                reduceType = orderedInfoResponse?.getWholeDiscountType(),
                reduceRate = orderedInfoResponse?.wholeDiscountReduce?.reduceRate,
                reduceDollar = orderedInfoResponse?.wholeDiscountReduce?.reduceDollar,
                reduceKhr = orderedInfoResponse?.wholeDiscountReduce?.reduceKhr,
                reduceVipDollar = orderedInfoResponse?.wholeDiscountReduce?.reduceVipDollar,
                reduceVipKhr = orderedInfoResponse?.wholeDiscountReduce?.reduceVipKhr,
                discountReduceActivityId = orderedInfoResponse?.discountReduceActivity?.id
            ).toRequestBody()
        )
        return executeResponse(response, {
            val goodsZhList =
                response.data.ordersZh?.getOrderedGoodJson()?.goodsList ?: arrayListOf()
            val goodsEnList =
                response.data.ordersEn?.getOrderedGoodJson()?.goodsList ?: arrayListOf()
            val goodsKmList =
                response.data.ordersKm?.getOrderedGoodJson()?.goodsList ?: arrayListOf()

            orderedInfoResponse?.offlinePaymentChannelsName =
                response.data.ordersZh?.offlinePaymentChannelsName
            orderedInfoResponse?.offlinePaymentChannelsNameEn =
                response.data.ordersEn?.offlinePaymentChannelsName
            orderedInfoResponse?.offlinePaymentChannelsNameKm =
                response.data.ordersKm?.offlinePaymentChannelsName

            orderedInfoResponse?.goods?.forEach {
                val zhIndex =
                    goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (zhIndex != -1) {
                    val goodsZh = goodsZhList[zhIndex]
                    it.name = goodsZh.name
                    it.tagItems = goodsZh.tagItems

                    val feeds = goodsZh.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedZhIndex = feeds.indexOf(feed)
                        if (feedZhIndex != -1) {
                            feeds[feedZhIndex].let { zh ->
                                feed.name = zh.name
                            }
                        }
                        Timber.e("feedZhIndex $feedZhIndex  ${feed.name}")
                    }
                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodZhIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodZhIndex != -1) {
                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
                                good.mealSetGoodsName = zh.mealSetGoodsName
                                good.mealSetTagItemList = zh.mealSetTagItemList
                            }
                        }
                    }
                }

                val enIndex =
                    goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (enIndex != -1) {
                    val goodsEn = goodsEnList[enIndex]
                    it.nameEn = goodsEn.name
                    it.tagItemsEn = goodsEn.tagItems

                    val feeds = goodsEn.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedEnIndex = feeds.indexOf(feed)
                        if (feedEnIndex != -1) {
                            feeds[feedEnIndex].let { en ->
                                feed.nameEn = en.name
                            }
                        }
                        Timber.e("feedEnIndex $feedEnIndex  ${feed.nameEn}")
                    }
                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodEnIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodEnIndex != -1) {
                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
                                good.mealSetGoodsNameEn = en.mealSetGoodsName
                                good.mealSetTagItemListEn = en.mealSetTagItemList
                            }
                        }
                    }
                }

                val kmIndex =
                    goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (kmIndex != -1) {
                    val goodsKm = goodsKmList[kmIndex]
                    it.nameKm = goodsKm.name
                    it.tagItemsKm = goodsKm.tagItems
                    val feeds = goodsKm.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedKhIndex = feeds.indexOf(feed)
                        if (feedKhIndex != -1) {
                            feeds[feedKhIndex].let { kh ->
                                feed.nameKh = kh.name
                            }
                        }
                        Timber.e("feedKhIndex $feedKhIndex  ${feed.nameKh}")
                    }
                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodKmIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodKmIndex != -1) {
                            orderMealSetGoodsDTOList[goodKmIndex].let { km ->
                                good.mealSetGoodsNameKm = km.mealSetGoodsName
                                good.mealSetTagItemListKm = km.mealSetTagItemList
                            }
                        }
                    }
                }
            }


            orderedInfoResponse?.currentOrderMoreList?.forEach {
                val zhIndex =
                    goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (zhIndex != -1) {
                    val goodsZh = goodsZhList[zhIndex]
                    it.name = goodsZh.name
                    it.tagItems = goodsZh.tagItems
                    val feeds = goodsZh.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedZhIndex = feeds.indexOf(feed)
                        if (feedZhIndex != -1) {
                            feeds[feedZhIndex].let { zh ->
                                feed.name = zh.name
                            }
                        }
                        Timber.e("currentOrderMoreList feedZhIndex $feedZhIndex  ${feed.name}")
                    }
                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodZhIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodZhIndex != -1) {
                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
                                good.mealSetGoodsName = zh.mealSetGoodsName
                                good.mealSetTagItemList = zh.mealSetTagItemList
                            }
                        }
                    }
                }

                val enIndex =
                    goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (enIndex != -1) {
                    val goodsEn = goodsEnList[enIndex]
                    it.nameEn = goodsEn.name
                    it.tagItemsEn = goodsEn.tagItems
                    val feeds = goodsEn.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedEnIndex = feeds.indexOf(feed)
                        if (feedEnIndex != -1) {
                            feeds[feedEnIndex].let { en ->
                                feed.nameEn = en.name
                            }
                        }
                        Timber.e("feedEnIndex ${feedEnIndex}  ${feed.nameEn}")
                    }
                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodEnIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodEnIndex != -1) {
                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
                                good.mealSetGoodsNameEn = en.mealSetGoodsName
                                good.mealSetTagItemListEn = en.mealSetTagItemList
                            }
                        }
                    }
                }

                val kmIndex =
                    goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                if (kmIndex != -1) {
                    val goodsKm = goodsKmList[kmIndex]
                    it.nameKm = goodsKm.name
                    it.tagItemsKm = goodsKm.tagItems
                    val feeds = goodsKm.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedKhIndex = feeds.indexOf(feed)
                        if (feedKhIndex != -1) {
                            feeds[feedKhIndex].let { kh ->
                                feed.nameKh = kh.name
                            }
                        }
                        Timber.e("feedKhIndex ${feedKhIndex}  ${feed.nameKh}")
                    }
                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodKmIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodKmIndex != -1) {
                            orderMealSetGoodsDTOList[goodKmIndex].let { en ->
                                good.mealSetGoodsNameKm = en.mealSetGoodsName
                                good.mealSetTagItemListKm = en.mealSetTagItemList
                            }
                        }
                    }
                }
            }

            val giftGoodsListZh =
                response.data.ordersZh?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()
            val giftGoodsListEn =
                response.data.ordersEn?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()
            val giftGoodsListKm =
                response.data.ordersKm?.getOrderedGoodJson()?.giftGoodsList ?: arrayListOf()

            orderedInfoResponse?.getGiftGoodsList()?.forEach {
                val zhIndex = giftGoodsListZh.indexOfFirst { value -> it.id == value.id }
                if (zhIndex != -1) {
                    val goodsZh = giftGoodsListZh[zhIndex]
                    it.name = goodsZh.name
                }

                val enIndex = giftGoodsListEn.indexOfFirst { value -> it.id == value.id }
                if (enIndex != -1) {
                    val goodsEn = giftGoodsListEn[enIndex]
                    it.nameEn = goodsEn.name
                }
                val kmIndex = giftGoodsListKm.indexOfFirst { value -> it.id == value.id }
                if (kmIndex != -1) {
                    val goodsKm = giftGoodsListKm[kmIndex]
                    it.nameKm = goodsKm.name
                }
            }


            val removeGoodListZh =
                response.data.ordersZh?.removeGoodList ?: arrayListOf()
            val removeGoodListEn =
                response.data.ordersEn?.removeGoodList ?: arrayListOf()
            val removeGoodListKm =
                response.data.ordersKm?.removeGoodList ?: arrayListOf()

            orderedInfoResponse?.removeGoodList?.forEach {
                val zhIndex =
                    removeGoodListZh.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                Timber.e("zhIndex  ${zhIndex}")
                if (zhIndex != -1) {
                    val goodsZh = removeGoodListZh[zhIndex]
                    it.name = goodsZh.name
                    it.tagItems = goodsZh.tagItems
                    val feeds = goodsZh.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedZhIndex = feeds.indexOf(feed)
                        if (feedZhIndex != -1) {
                            feeds[feedZhIndex].let { zh ->
                                feed.name = zh.name
                            }
                        }
                        Timber.e("removeGoodList feedZhIndex $feedZhIndex  ${feed.name}")
                    }
                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodZhIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodZhIndex != -1) {
                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
                                good.mealSetGoodsName = zh.mealSetGoodsName
                                good.mealSetTagItemList = zh.mealSetTagItemList
                            }
                        }
                    }
                }

                val enIndex =
                    removeGoodListEn.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                Timber.e("enIndex  ${enIndex}")
                if (enIndex != -1) {
                    val goodsEn = removeGoodListEn[enIndex]
                    it.nameEn = goodsEn.name
                    it.tagItemsEn = goodsEn.tagItems
                    val feeds = goodsEn.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedEnIndex = feeds.indexOf(feed)
                        if (feedEnIndex != -1) {
                            feeds[feedEnIndex].let { en ->
                                feed.nameEn = en.name
                            }
                        }
                        Timber.e("feedEnIndex ${feedEnIndex}  ${feed.nameEn}")
                    }
                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodEnIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodEnIndex != -1) {
                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
                                good.mealSetGoodsNameEn = en.mealSetGoodsName
                                good.mealSetTagItemListEn = en.mealSetTagItemList
                            }
                        }
                    }
                }

                val kmIndex =
                    removeGoodListKm.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
                Timber.e("kmIndex  ${kmIndex}")
                if (kmIndex != -1) {
                    val goodsKm = removeGoodListKm[kmIndex]
                    it.nameKm = goodsKm.name
                    it.tagItemsKm = goodsKm.tagItems
                    val feeds = goodsKm.feeds ?: arrayListOf()
                    it.feeds?.forEach { feed ->
                        val feedKhIndex = feeds.indexOf(feed)
                        if (feedKhIndex != -1) {
                            feeds[feedKhIndex].let { kh ->
                                feed.nameKh = kh.name
                            }
                        }
                        Timber.e("feedKhIndex ${feedKhIndex}  ${feed.nameKh}")
                    }
                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
                    it.orderMealSetGoodsDTOList?.forEach { good ->
                        val goodKmIndex =
                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
                        if (goodKmIndex != -1) {
                            orderMealSetGoodsDTOList[goodKmIndex].let { en ->
                                good.mealSetGoodsNameKm = en.mealSetGoodsName
                                good.mealSetTagItemListKm = en.mealSetTagItemList
                            }
                        }
                    }
                }
            }


//            orderedInfoResponse?.couponActivityList =
//                response.data.ordersZh?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()
//            orderedInfoResponse?.couponActivityListEn =
//                response.data.ordersEn?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()
//            orderedInfoResponse?.couponActivityListKm =
//                response.data.ordersKm?.getOrderedGoodJson()?.couponActivityList ?: arrayListOf()

            Timber.e("菜品翻译结束")
        })
    }

//
//    /**
//     * 购物车打印预结单  和翻译用同一个接口
//     */
//    suspend fun cartPrintPreSettlement(
//        diningStyle: Int,
//        goodsList: List<GoodsBo>,
//        note: String?,
//        tableUuid: String?,
//        //优惠券码
//        couponCode: String? = null,
//    ): ApiResponse<OrderedTranslateResponse> {
//        return safeApiCall(
//            call = {
//                requestCartPrintPreSettlement(
//                    diningStyle = diningStyle,
//                    goodsList = goodsList,
//                    note = note,
//                    tableUuid = tableUuid,
//                    //优惠券码
//                    couponCode = couponCode,
//                )
//            },
//            errorMessage = ""
//        )
//    }

//    private suspend fun requestCartPrintPreSettlement(
//        diningStyle: Int,
//        goodsList: List<GoodsBo>,
//        note: String?,
//        tableUuid: String?,
//        //优惠券码
//        couponCode: String? = null,
//    ): ApiResponse<OrderedTranslateResponse> {
//        val response = apiService.orderTranslate(
//            CartPrintPreSettlementRequest(
//                diningStyle = diningStyle,
//                goodsList = goodsList,
//                note = note,
//                tableUuid = tableUuid,
//                //优惠券码
//                couponCode = couponCode,
//            ).toRequestBody()
//        )
//        return executeResponse(
//            response,
////            {
////            val goodsZhList =
////                response.data.ordersZh?.getGoodsVo()?.goodsList ?: arrayListOf()
////            val goodsEnList =
////                response.data.ordersEn?.getGoodsVo()?.goodsList ?: arrayListOf()
////            val goodsKmList =
////                response.data.ordersKm?.getGoodsVo()?.goodsList ?: arrayListOf()
////
//////            response.data.ordersZh?.offlinePaymentChannelsName =
//////                response.data.ordersZh?.offlinePaymentChannelsName
////            response.data.ordersZh?.offlinePaymentChannelsNameEn =
////                response.data.ordersEn?.offlinePaymentChannelsName
////            response.data.ordersZh?.offlinePaymentChannelsNameKm =
////                response.data.ordersKm?.offlinePaymentChannelsName
////            response.data.ordersZh?.goods = ArrayList(goodsZhList)
////
//////            response.data.ordersZh?.goods?.forEach {
//////                val zhIndex =
//////                    goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                if (zhIndex != -1) {
//////                    val goodsZh = goodsZhList[zhIndex]
//////                    it.name = goodsZh.name
//////                    it.tagItems = goodsZh.tagItems
//////
//////                    val feeds = goodsZh.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedZhIndex = feeds.indexOf(feed)
//////                        if (feedZhIndex != -1) {
//////                            feeds[feedZhIndex].let { zh ->
//////                                feed.name = zh.name
//////                            }
//////                        }
//////                        Timber.e("feedZhIndex $feedZhIndex  ${feed.name}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodZhIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodZhIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
//////                                good.mealSetGoodsName = zh.mealSetGoodsName
//////                                good.mealSetTagItemList = zh.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////
//////                val enIndex =
//////                    goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                if (enIndex != -1) {
//////                    val goodsEn = goodsEnList[enIndex]
//////                    it.nameEn = goodsEn.name
//////                    it.tagItemsEn = goodsEn.tagItems
//////
//////                    val feeds = goodsEn.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedEnIndex = feeds.indexOf(feed)
//////                        if (feedEnIndex != -1) {
//////                            feeds[feedEnIndex].let { en ->
//////                                feed.nameEn = en.name
//////                            }
//////                        }
//////                        Timber.e("feedEnIndex $feedEnIndex  ${feed.nameEn}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodEnIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodEnIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
//////                                good.mealSetGoodsNameEn = en.mealSetGoodsName
//////                                good.mealSetTagItemListEn = en.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////
//////                val kmIndex =
//////                    goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                if (kmIndex != -1) {
//////                    val goodsKm = goodsKmList[kmIndex]
//////                    it.nameKm = goodsKm.name
//////                    it.tagItemsKm = goodsKm.tagItems
//////                    val feeds = goodsKm.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedKhIndex = feeds.indexOf(feed)
//////                        if (feedKhIndex != -1) {
//////                            feeds[feedKhIndex].let { kh ->
//////                                feed.nameKh = kh.name
//////                            }
//////                        }
//////                        Timber.e("feedKhIndex $feedKhIndex  ${feed.nameKh}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodKmIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodKmIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodKmIndex].let { km ->
//////                                good.mealSetGoodsNameKm = km.mealSetGoodsName
//////                                good.mealSetTagItemListKm = km.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////            }
////
////
////            response.data.ordersZh?.currentOrderMoreList?.forEach {
////                val zhIndex =
////                    goodsZhList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
////                if (zhIndex != -1) {
////                    val goodsZh = goodsZhList[zhIndex]
////                    it.name = goodsZh.name
////                    it.tagItems = goodsZh.tagItems
////                    val feeds = goodsZh.feeds ?: arrayListOf()
////                    it.feeds?.forEach { feed ->
////                        val feedZhIndex = feeds.indexOf(feed)
////                        if (feedZhIndex != -1) {
////                            feeds[feedZhIndex].let { zh ->
////                                feed.name = zh.name
////                            }
////                        }
////                        Timber.e("currentOrderMoreList feedZhIndex $feedZhIndex  ${feed.name}")
////                    }
////                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
////                    it.orderMealSetGoodsDTOList?.forEach { good ->
////                        val goodZhIndex =
////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
////                        if (goodZhIndex != -1) {
////                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
////                                good.mealSetGoodsName = zh.mealSetGoodsName
////                                good.mealSetTagItemList = zh.mealSetTagItemList
////                            }
////                        }
////                    }
////                }
////
////                val enIndex =
////                    goodsEnList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
////                if (enIndex != -1) {
////                    val goodsEn = goodsEnList[enIndex]
////                    it.nameEn = goodsEn.name
////                    it.tagItemsEn = goodsEn.tagItems
////                    val feeds = goodsEn.feeds ?: arrayListOf()
////                    it.feeds?.forEach { feed ->
////                        val feedEnIndex = feeds.indexOf(feed)
////                        if (feedEnIndex != -1) {
////                            feeds[feedEnIndex].let { en ->
////                                feed.nameEn = en.name
////                            }
////                        }
////                        Timber.e("feedEnIndex ${feedEnIndex}  ${feed.nameEn}")
////                    }
////                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
////                    it.orderMealSetGoodsDTOList?.forEach { good ->
////                        val goodEnIndex =
////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
////                        if (goodEnIndex != -1) {
////                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
////                                good.mealSetGoodsNameEn = en.mealSetGoodsName
////                                good.mealSetTagItemListEn = en.mealSetTagItemList
////                            }
////                        }
////                    }
////                }
////
////                val kmIndex =
////                    goodsKmList.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
////                if (kmIndex != -1) {
////                    val goodsKm = goodsKmList[kmIndex]
////                    it.nameKm = goodsKm.name
////                    it.tagItemsKm = goodsKm.tagItems
////                    val feeds = goodsKm.feeds ?: arrayListOf()
////                    it.feeds?.forEach { feed ->
////                        val feedKhIndex = feeds.indexOf(feed)
////                        if (feedKhIndex != -1) {
////                            feeds[feedKhIndex].let { kh ->
////                                feed.nameKh = kh.name
////                            }
////                        }
////                        Timber.e("feedKhIndex ${feedKhIndex}  ${feed.nameKh}")
////                    }
////                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
////                    it.orderMealSetGoodsDTOList?.forEach { good ->
////                        val goodKmIndex =
////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
////                        if (goodKmIndex != -1) {
////                            orderMealSetGoodsDTOList[goodKmIndex].let { en ->
////                                good.mealSetGoodsNameKm = en.mealSetGoodsName
////                                good.mealSetTagItemListKm = en.mealSetTagItemList
////                            }
////                        }
////                    }
////                }
////            }
////
////            val giftGoodsListZh =
////                response.data.ordersZh?.getGoodsVo()?.giftGoodsList ?: arrayListOf()
////            val giftGoodsListEn =
////                response.data.ordersEn?.getGoodsVo()?.giftGoodsList ?: arrayListOf()
////            val giftGoodsListKm =
////                response.data.ordersKm?.getGoodsVo()?.giftGoodsList ?: arrayListOf()
////
////            response.data.ordersZh?.giftGoods = response.data.ordersZh?.getGoodsVo()?.giftGoodsList
////
////            response.data.ordersZh?.getGiftGoodsList()?.forEach {
////                val zhIndex = giftGoodsListZh.indexOfFirst { value -> it.id == value.id }
////                if (zhIndex != -1) {
////                    val goodsZh = giftGoodsListZh[zhIndex]
////                    it.name = goodsZh.name
////                }
////
////                val enIndex = giftGoodsListEn.indexOfFirst { value -> it.id == value.id }
////                if (enIndex != -1) {
////                    val goodsEn = giftGoodsListEn[enIndex]
////                    it.nameEn = goodsEn.name
////                }
////                val kmIndex = giftGoodsListKm.indexOfFirst { value -> it.id == value.id }
////                if (kmIndex != -1) {
////                    val goodsKm = giftGoodsListKm[kmIndex]
////                    it.nameKm = goodsKm.name
////                }
////            }
////
//////
//////            val removeGoodListZh =
//////                response.data.ordersZh?.removeGoodList ?: arrayListOf()
//////            val removeGoodListEn =
//////                response.data.ordersEn?.removeGoodList ?: arrayListOf()
//////            val removeGoodListKm =
//////                response.data.ordersKm?.removeGoodList ?: arrayListOf()
//////
//////            response.data.ordersZh?.removeGoodList?.forEach {
//////                val zhIndex =
//////                    removeGoodListZh.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                Timber.e("zhIndex  ${zhIndex}")
//////                if (zhIndex != -1) {
//////                    val goodsZh = removeGoodListZh[zhIndex]
//////                    it.name = goodsZh.name
//////                    it.tagItems = goodsZh.tagItems
//////                    val feeds = goodsZh.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedZhIndex = feeds.indexOf(feed)
//////                        if (feedZhIndex != -1) {
//////                            feeds[feedZhIndex].let { zh ->
//////                                feed.name = zh.name
//////                            }
//////                        }
//////                        Timber.e("removeGoodList feedZhIndex $feedZhIndex  ${feed.name}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsZh.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodZhIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodZhIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodZhIndex].let { zh ->
//////                                good.mealSetGoodsName = zh.mealSetGoodsName
//////                                good.mealSetTagItemList = zh.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////
//////                val enIndex =
//////                    removeGoodListEn.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                Timber.e("enIndex  ${enIndex}")
//////                if (enIndex != -1) {
//////                    val goodsEn = removeGoodListEn[enIndex]
//////                    it.nameEn = goodsEn.name
//////                    it.tagItemsEn = goodsEn.tagItems
//////                    val feeds = goodsEn.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedEnIndex = feeds.indexOf(feed)
//////                        if (feedEnIndex != -1) {
//////                            feeds[feedEnIndex].let { en ->
//////                                feed.nameEn = en.name
//////                            }
//////                        }
//////                        Timber.e("feedEnIndex ${feedEnIndex}  ${feed.nameEn}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsEn.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodEnIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodEnIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodEnIndex].let { en ->
//////                                good.mealSetGoodsNameEn = en.mealSetGoodsName
//////                                good.mealSetTagItemListEn = en.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////
//////                val kmIndex =
//////                    removeGoodListKm.indexOfFirst { good -> good.goodHashKey == it.goodHashKey }
//////                Timber.e("kmIndex  ${kmIndex}")
//////                if (kmIndex != -1) {
//////                    val goodsKm = removeGoodListKm[kmIndex]
//////                    it.nameKm = goodsKm.name
//////                    it.tagItemsKm = goodsKm.tagItems
//////                    val feeds = goodsKm.feeds ?: arrayListOf()
//////                    it.feeds?.forEach { feed ->
//////                        val feedKhIndex = feeds.indexOf(feed)
//////                        if (feedKhIndex != -1) {
//////                            feeds[feedKhIndex].let { kh ->
//////                                feed.nameKh = kh.name
//////                            }
//////                        }
//////                        Timber.e("feedKhIndex ${feedKhIndex}  ${feed.nameKh}")
//////                    }
//////                    val orderMealSetGoodsDTOList = goodsKm.orderMealSetGoodsDTOList ?: listOf()
//////                    it.orderMealSetGoodsDTOList?.forEach { good ->
//////                        val goodKmIndex =
//////                            orderMealSetGoodsDTOList.indexOfFirst { it.mealSetGoodsId == good.mealSetGoodsId && it.mealSetGroupId == good.mealSetGroupId && it.getTagIds() == good.getTagIds() }
//////                        if (goodKmIndex != -1) {
//////                            orderMealSetGoodsDTOList[goodKmIndex].let { en ->
//////                                good.mealSetGoodsNameKm = en.mealSetGoodsName
//////                                good.mealSetTagItemListKm = en.mealSetTagItemList
//////                            }
//////                        }
//////                    }
//////                }
//////            }
//////
////
////            Timber.e("菜品翻译结束")
////        }
//        )
//    }
//

    suspend fun getAbNormalOrderV2(
        tableUuid: String?,
        orderNo: String? = null,
        diningStyle: Int? = null,
        payStatusList: List<Int>? = null
    ):
            ApiResponse<List<OrderedInfoResponse>> {
        return safeApiCall(
            call = { requestAbNormalOrderV2(tableUuid, orderNo, diningStyle, payStatusList) },
            errorMessage = ""
        )
    }

    private suspend fun requestAbNormalOrderV2(
        tableUuid: String?,
        orderNo: String? = null,
        diningStyle: Int? = null,
        payStatusList: List<Int>? = null
    ): ApiResponse<List<OrderedInfoResponse>> {
        val model =
            AbNormalOrderV2Request(tableUuid, orderNo, diningStyle, payStatusList).toRequestBody()
        val response =
            apiService.abnormalOrderV2(model)
        return executeResponse(response)
    }


    suspend fun checkVersion(
        versionName: String?,
        terminal: Int?,
    ):
            ApiResponse<VersionCheckResponse> {
        return safeApiCall(
            call = { requestCheckVersion(versionName, terminal) },
            errorMessage = ""
        )
    }

    private suspend fun requestCheckVersion(
        versionName: String?,
        terminal: Int?,
    ): ApiResponse<VersionCheckResponse> {
        val model =
            CheckVersionRequest(versionName, terminal).toRequestBody()
        val response =
            apiService.checkVersion(model)
        return executeResponse(response)
    }


    suspend fun getCustomerInfo(
        newTableUuid: String?,
        customerInfoVo: CustomerInfoVo?,
        diningStyle: Int?
    ):
            ApiResponse<CustomerInfoVo> {
        return safeApiCall(
            call = { requestGetCustomerInfo(newTableUuid, customerInfoVo, diningStyle) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetCustomerInfo(
        newTableUuid: String?,
        customerInfoVo: CustomerInfoVo?,
        diningStyle: Int?
    ): ApiResponse<CustomerInfoVo> {
        val model =
            CustomerInfoRequest(newTableUuid, customerInfoVo, diningStyle).toRequestBody()
        val response =
            apiService.getCustomerInfo(model)
        return executeResponse(response)
    }


    suspend fun getPaymentMethodList():
            ApiResponse<List<PaymentChannelModel>> {
        return safeApiCall(
            call = { requestGetPaymentMethodList() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetPaymentMethodList(): ApiResponse<List<PaymentChannelModel>> {
        val response =
            apiService.getPaymentMethodList(null)
        return executeResponse(response)
    }

    suspend fun getReduceDiscountDetail(orderNo: String?):
            ApiResponse<ReduceDiscountDetailModel> {
        return safeApiCall(
            call = { requestGetReduceDiscountDetail(orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetReduceDiscountDetail(orderNo: String?): ApiResponse<ReduceDiscountDetailModel> {
        val response =
            apiService.getReduceDiscountDetail(orderNo)
        return executeResponse(response)
    }

    suspend fun getReduceDiscountConfirm(
        orderNo: String? = null,
        reduceDollar: BigDecimal? = null,
        reduceVipDollar: BigDecimal? = null,
        reduceKhr: BigDecimal? = null,
        reduceVipKhr: BigDecimal? = null,
        reduceRate: Double? = null,
        reduceType: Int? = null,
        discountType: Int? = null,
        couponId: Long? = null,
        reduceReason: String? = null,
        discountReduceActivityId: String? = null
    ):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestGetReduceDiscountConfirm(
                    orderNo = orderNo,
                    reduceDollar = reduceDollar,
                    reduceVipDollar = reduceVipDollar,
                    reduceKhr = reduceKhr,
                    reduceVipKhr = reduceVipKhr,
                    reduceRate = reduceRate,
                    discountType = discountType,
                    couponId = couponId,
                    reduceType = reduceType,
                    reduceReason = reduceReason,
                    discountReduceActivityId = discountReduceActivityId
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetReduceDiscountConfirm(
        orderNo: String? = null,
        reduceDollar: BigDecimal? = null,
        reduceVipDollar: BigDecimal? = null,
        reduceKhr: BigDecimal? = null,
        reduceVipKhr: BigDecimal? = null,
        reduceRate: Double? = null,
        reduceType: Int? = null,
        discountType: Int? = null,
        couponId: Long? = null,
        reduceReason: String? = null,
        discountReduceActivityId: String? = null
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.getReduceDiscountConfirm(
                ReduceDiscountDetailRequest(
                    orderNo = orderNo,
                    reduceDollar = reduceDollar,
                    reduceVipDollar = reduceVipDollar,
                    reduceKhr = reduceKhr,
                    reduceVipKhr = reduceVipKhr,
                    reduceRate = reduceRate,
                    discountType = discountType,
                    couponId = couponId,
                    reduceType = reduceType,
                    reduceReason = reduceReason,
                    discountReduceActivityId = discountReduceActivityId
                ).toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }


    suspend fun setSingleItemReduceDiscount(
        orderNo: String? = null,
        singleReduceReason: String? = null,
        singleItemDiscountList: List<SingleDiscountRequest>?
    ):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestSetSingleItemReduceDiscount(
                    orderNo,
                    singleReduceReason,
                    singleItemDiscountList
                )
            },
            errorMessage = ""
        )
    }


    private suspend fun requestSetSingleItemReduceDiscount(
        orderNo: String? = null,
        singleReduceReason: String? = null,
        singleItemDiscountList: List<SingleDiscountRequest>?
    ): ApiResponse<BaseBooleanResponse> {
        Timber.e("singleItemDiscountList: ${singleItemDiscountList?.map { it.toString() }}")
        val res = DiscountSingleParamRequest(
            orderNo,
            singleReduceReason,
            singleItemDiscountList,
        )
        Timber.e("singleItemDiscountList: ${res.toJson()}")
        val response =
            apiService.setSingleItemReduceDiscount(
                DiscountSingleParamRequest(
                    orderNo,
                    singleReduceReason,
                    singleItemDiscountList,
                ).toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun getUsbPrinterInfo():
            ApiResponse<PrinterConfigInfo> {
        return safeApiCall(
            call = { requestGetUsbPrinterInfo() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetUsbPrinterInfo(): ApiResponse<PrinterConfigInfo> {
        val response =
            apiService.getUsbPrinterInfo()
        return executeResponse(response)
    }

    suspend fun getPrinterInfoList():
            ApiResponse<List<PrinterConfigInfo>> {
        return safeApiCall(
            call = { requestGetPrinterInfoList() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetPrinterInfoList(): ApiResponse<List<PrinterConfigInfo>> {
        val response =
            apiService.getPrinterInfoList()
        return executeResponse(response)
    }

    suspend fun getUnReadNumAndUnPrint():
            ApiResponse<UnReadAndPrintResponse> {
        return safeApiCall(
            call = { requestGetUnReadNumAndUnPrint() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetUnReadNumAndUnPrint(): ApiResponse<UnReadAndPrintResponse> {
        val response =
            apiService.getUnReadNumAndUnPrint()
        return executeResponse(response)
    }

    suspend fun updatePrintLog(orderId: String?):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestUpdatePrintLog(orderId) },
            errorMessage = ""
        )
    }

    private suspend fun requestUpdatePrintLog(orderId: String?): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.updatePrintLog(orderId)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun addConsumer(userName: String?, phone: String?):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestAddConsumer(userName, phone) },
            errorMessage = ""
        )
    }

    private suspend fun requestAddConsumer(
        userName: String?,
        phone: String?
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.addConsumer(CreateMemberRequest(userName, phone).toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun updateConsumer(
        consumerId: String? = null,
        type: Int? = null,
        nickname: String? = null,
        phone: String? = null,
        otp: String? = null,
    ):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestUpdateConsumer(consumerId, type, nickname, phone, otp) },
            errorMessage = ""
        )
    }

    private suspend fun requestUpdateConsumer(
        consumerId: String? = null,
        type: Int? = null,
        nickname: String? = null,
        phone: String? = null,
        otp: String? = null,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.updateConsumer(
                UpdateMemberRequest(
                    consumerId,
                    type,
                    nickname,
                    phone,
                    otp
                ).toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun sendSmsCode(
        consumerId: String? = null,
        phone: String? = null,
    ):
            ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestSendSmsCode(consumerId, phone) },
            errorMessage = ""
        )
    }

    private suspend fun requestSendSmsCode(
        consumerId: String? = null,
        phone: String? = null,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.sendSmsCode(
                consumerId,
                phone,
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }


    suspend fun preSettlement(
        preSettlementRequest: PreSettlementRequest
    ):
            ApiResponse<OrderedInfoResponse> {
//        Timber.e("reduceDollar: $reduceDollar")
        return safeApiCall(
            call = {
                requestPreSettlement(
                    preSettlementRequest
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestPreSettlement(
        preSettlementRequest: PreSettlementRequest
    ): ApiResponse<OrderedInfoResponse> {
        val response =
            apiService.preSettlement(
                preSettlementRequest.toRequestBody()
            )
        return executeResponse(response, {

        })
    }


    /**
     * 获取khqr 付款码
     */
    suspend fun getKhqr(
        preSettlementRequest: PreSettlementRequest
    ): ApiResponse<PaymentResponse> {
        return safeApiCall(
            call = {
                requestGetKhqr(
                    preSettlementRequest
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetKhqr(
        preSettlementRequest: PreSettlementRequest
    ): ApiResponse<PaymentResponse> {
        val response = apiService.getKhqr(
            preSettlementRequest.toRequestBody()
        )
        return executeResponse(response, {

        })
    }

    /**
     *
     */
    suspend fun getStoreInfo(
    ): ApiResponse<StoreInfoResponse> {
        return safeApiCall(
            call = { requestGetStoreInfo() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetStoreInfo(
    ): ApiResponse<StoreInfoResponse> {
        val response = apiService.getStoreInfo()
        return executeResponse(response, {

        })
    }

    /**
     *识别优惠券
     */
    suspend fun couponCodeRecognition(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        couponCode: String? = null,
        orderNo: String? = null
    ): ApiResponse<CouponModel> {
        return safeApiCall(
            call = {
                requestCouponCodeRecognition(
                    diningStyle,
                    goodsList,
                    isPreOrder,
                    note,
                    tableUuid,
                    couponCode,
                    orderNo
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCouponCodeRecognition(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        couponCode: String? = null,
        orderNo: String? = null
    ): ApiResponse<CouponModel> {
        val response = apiService.couponCodeRecognition(
            CouponCodeRecongnitionRequest(
                diningStyle,
                goodsList,
                isPreOrder,
                tableUuid,
                couponCode,
                orderNo
            ).toRequestBody()
        )
        return executeResponse(response, {

        })
    }


    /**
     *获取订单可用优惠券列表
     */
    suspend fun findOrderCanUseCoupon(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        orderNo: String? = null
    ): ApiResponse<List<CouponModel>> {
        return safeApiCall(
            call = {
                requestFindOrderCanUseCoupon(
                    diningStyle = diningStyle,
                    goodsList = goodsList,
                    isPreOrder = isPreOrder,
                    note = note,
                    tableUuid = tableUuid,
                    orderNo = orderNo
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestFindOrderCanUseCoupon(
        diningStyle: Int? = null,
        goodsList: List<GoodsBo>? = null,
        isPreOrder: Boolean? = null,
        note: String? = null,
        tableUuid: String? = null,
        orderNo: String? = null
    ): ApiResponse<List<CouponModel>> {
        val response = apiService.findOrderCanUseCoupon(
            CouponCodeRecongnitionRequest(
                diningStyle = diningStyle,
                goodsList = goodsList,
                isPreOrder = isPreOrder,
                tableUuid = tableUuid,
                orderNo = orderNo
            ).toRequestBody()
        )
        return executeResponse(response, {

        })
    }


    /**
     *获取用户充值可用优惠券
     */
    suspend fun findTopUpCanUseCoupon(
        addNum: BigDecimal? = null,
        id: String? = null
    ): ApiResponse<List<CouponModel>> {
        return safeApiCall(
            call = {
                requestFindTopUpCanUseCoupon(
                    addNum = addNum,
                    id = id,
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestFindTopUpCanUseCoupon(
        addNum: BigDecimal? = null,
        id: String? = null
    ): ApiResponse<List<CouponModel>> {
        val response = apiService.findTopUpCanUseCoupon(
            TopUpCanUseCouponRequest(
                addNum = addNum,
                id = id,
            ).toRequestBody()
        )
        return executeResponse(response, {

        })
    }

    /**
     * 修改订单
     */
    suspend fun updateNote(
        orderNo: String?,
        note: String? = ""
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestUpdateNote(orderNo, note) },
            errorMessage = ""
        )
    }

    private suspend fun requestUpdateNote(
        orderNo: String?,
        note: String? = ""
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.updateNote(UpdateNoteRequest(orderNo, note).toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    /**
     * 获取接单列表
     */
    suspend fun acceptOrderList(acceptOrderListRequest: AcceptOrderListRequest): ApiResponse<OrderedTotalResponse> {
        return safeApiCall(
            call = { requestAcceptOrderList(acceptOrderListRequest) },
            errorMessage = ""
        )
    }

    private suspend fun requestAcceptOrderList(acceptOrderListRequest: AcceptOrderListRequest): ApiResponse<OrderedTotalResponse> {
        val response = apiService.getAcceptOrderList(acceptOrderListRequest.toRequestBody())
        return executeResponse(response)
    }

    /**
     * 接单信息
     */
    suspend fun getAcceptOrderInfo(
        id: String? = null,
        orderNo: String? = null,
        isReadOrder: Boolean? = null,
    ): ApiResponse<OrderedInfoResponse> {
        return safeApiCall(
            call = { requestGetAcceptOrderInfo(id, orderNo, isReadOrder) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetAcceptOrderInfo(
        id: String? = null,
        orderNo: String? = null,
        isReadOrder: Boolean? = null,
    ): ApiResponse<OrderedInfoResponse> {
        val response =
            apiService.getAcceptOrderInfo(id, orderNo, isReadOrder)
        return executeResponse(response)
    }


    /**
     * 接单
     */
    suspend fun acceptOrder(
        id: String? = null,
        orderNo: String? = null,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestAcceptOrder(id, orderNo) },
            errorMessage = ""
        )
    }

    private suspend fun requestAcceptOrder(
        id: String? = null,
        orderNo: String? = null
    ): ApiResponse<BaseBooleanResponse> {
        val response = apiService.acceptOrder(
            AcceptOrderInfoRequest(
                id = id,
                orderNo = orderNo
            ).toRequestBody()
        )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    /**
     * 取消接单
     */
    suspend fun cancelAcceptOrder(
        id: String? = null,
        cancelReason: String? = null
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestCancelAcceptOrder(id, cancelReason) },
            errorMessage = ""
        )
    }

    private suspend fun requestCancelAcceptOrder(
        id: String? = null,
        cancelReason: String?
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.cancelAcceptOrder(CancelAcceptOrderRequest(id, cancelReason).toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun getAcceptOrderUnReadNumAndUnPrint():
            ApiResponse<UnReadAndPrintResponse> {
        return safeApiCall(
            call = { requestGetAcceptOrderUnReadNumAndUnPrint() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetAcceptOrderUnReadNumAndUnPrint(): ApiResponse<UnReadAndPrintResponse> {
        val response =
            apiService.getAcceptOrderUnReadNumAndUnPrint()
        return executeResponse(response)
    }


    suspend fun getNoticeList(page: Int?, pageSize: Int?):
            ApiResponse<NoticeListResponse> {
        return safeApiCall(
            call = { requestGetNoticeList(page, pageSize) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetNoticeList(
        page: Int?,
        pageSize: Int?
    ): ApiResponse<NoticeListResponse> {
        val response =
            apiService.getNoticeList(page, pageSize)
        return executeResponse(response)
    }


    suspend fun getLastNotice():
            ApiResponse<NoticeResponse> {
        return safeApiCall(
            call = { requestGetLastNotice() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetLastNotice(
    ): ApiResponse<NoticeResponse> {
        val response =
            apiService.getLastNotice()
        return executeResponse(response)
    }

    suspend fun getNoticeUnread():
            ApiResponse<Int> {
        return safeApiCall(
            call = { requestGetNoticeUnread() },
            errorMessage = ""
        )
    }

    private suspend fun requestGetNoticeUnread(
    ): ApiResponse<Int> {
        val response =
            apiService.getNoticeUnread()
        return executeResponse(response)
    }


    suspend fun getNoticeDetail(
        id: String,
        read: Boolean
    ): ApiResponse<NoticeResponse> {
        return safeApiCall(
            call = { requestGetNoticeDetail(id, read) },
            errorMessage = ""
        )
    }

    private suspend fun requestGetNoticeDetail(
        id: String,
        read: Boolean
    ): ApiResponse<NoticeResponse> {
        val response =
            apiService.getNoticeDetail(id, read)
        return executeResponse(response)
    }

    suspend fun reverseCheckOut(
        orderNo: String,
        reasonType: Int,
        reason: String?,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestReverseCheckOut(orderNo, reasonType, reason) },
            errorMessage = ""
        )
    }

    private suspend fun requestReverseCheckOut(
        orderNo: String,
        reasonType: Int,
        reason: String?,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.reverseCheckOut(
                AnitSettlementRequest(
                    orderNo,
                    reasonType,
                    reason
                ).toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun getUploadPath(
        @Query("fileName") fileName: String,
        @Query("fileSize") fileSize: Long,
    ): ApiResponse<UploadTokenResponse> {
        return safeApiCall(
            call = {
                requestGetUploadPath(
                    fileName = fileName,
                    fileSize = fileSize
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetUploadPath(
        @Query("fileName") fileName: String,
        @Query("fileSize") fileSize: Long,
    ): ApiResponse<UploadTokenResponse> {
        val response =
            apiService.getUploadPath(
                fileName = fileName,
                fileSize = fileSize
            )
        return executeResponse(response)
    }

    suspend fun getI18nData(
        columnId: String,
    ): ApiResponse<StoreInfoMultLanguageListRespnose> {
        return safeApiCall(
            call = {
                requestI18nData(
                    columnId = columnId,
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestI18nData(
        columnId: String,
    ): ApiResponse<StoreInfoMultLanguageListRespnose> {
        val response =
            apiService.i18nData(
                columnId = columnId
            )
        return executeResponse(response)
    }

    suspend fun setI18nData(
        list: List<StoreInfoMultLanguageRespnose>? = listOf()
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestSetI18nData(
                    list
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestSetI18nData(
        list: List<StoreInfoMultLanguageRespnose>? = listOf()
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.setI18nData(
                list?.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun saveStore(
        storeInfoResponse: StoreInfoResponse,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestSaveStore(
                    storeInfoResponse
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestSaveStore(
        storeInfoResponse: StoreInfoResponse,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.saveStore(
                storeInfoResponse.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun mergeAbleOrderList(
        orderId: String?,
        payStatus: List<Int>? = null,
        tableUuids: List<String>? = null
    ): ApiResponse<MergeOrderListResponse> {
        return safeApiCall(
            call = {
                requestMergeAbleOrderList(
                    orderId, payStatus, tableUuids
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestMergeAbleOrderList(
        orderId: String?,
        payStatus: List<Int>? = null,
        tableUuids: List<String>? = null
    ): ApiResponse<MergeOrderListResponse> {
        val response =
            apiService.mergeAbleOrderList(
                MergeOrderListRequest(orderId, payStatus, tableUuids).toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun mergeOrder(
        mergeOrderRequest: MergeOrderRequest,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestMergeOrder(
                    mergeOrderRequest
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestMergeOrder(
        mergeOrderRequest: MergeOrderRequest,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.mergeOrder(
                mergeOrderRequest.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun splitOrder(
        orderId: String?,
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestSplitOrder(
                    orderId
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestSplitOrder(
        orderId: String?,
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.splitOrder(
                orderId
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun getLogoBase64(
    ): ApiResponse<String> {
        return safeApiCall(
            call = {
                requestGetLogoBase64(
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetLogoBase64(
    ): ApiResponse<String> {
        val response =
            apiService.getLogoBase64(
            )
        return executeResponse(response)
    }


    suspend fun getSummarySalesReportPrint(
        startTime: String, endTime: String, keyword: String, groupIds: String, orderByColumn: String
    ): ApiResponse<ProductReportResponse> {
        return safeApiCall(
            call = {
                requestGetSummarySalesReportPrint(
                    startTime, endTime, keyword, groupIds, orderByColumn
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetSummarySalesReportPrint(
        startTime: String, endTime: String, keyword: String, groupIds: String, orderByColumn: String
    ): ApiResponse<ProductReportResponse> {
        val response =
            apiService.getSummarySalesReportPrint(
                startTime, endTime, keyword, groupIds, orderByColumn
            )
        return executeResponse(response)
    }


    suspend fun getPayMethodReportPrint(
        startTime: String, endTime: String
    ): ApiResponse<PaymentMethodReportResponse> {
        return safeApiCall(
            call = {
                requestGetPayMethodReportPrint(
                    startTime, endTime
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetPayMethodReportPrint(
        startTime: String, endTime: String
    ): ApiResponse<PaymentMethodReportResponse> {
        val response =
            apiService.getPayMethodReportPrint(
                startTime, endTime
            )
        return executeResponse(response)
    }


    suspend fun getUnreadCashierMsg(): ApiResponse<List<UnreadCashierMsgResponse>> {
        return safeApiCall(
            call = {
                requestUnreadCashierMsg()
            },
            errorMessage = ""
        )
    }

    private suspend fun requestUnreadCashierMsg(
    ): ApiResponse<List<UnreadCashierMsgResponse>> {
        val response = apiService.getUnreadCashierMsg()
        return executeResponse(response)
    }


    suspend fun getGoodsTemporaryList(
    ): ApiResponse<List<Goods>> {
        return safeApiCall(
            call = {
                requestGetGoodsTemporaryList()
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetGoodsTemporaryList(
    ): ApiResponse<List<Goods>> {
        val response =
            apiService.getGoodsTemporaryList(
            )
        return executeResponse(response)
    }


    suspend fun createGoodsTemporary(
        request: CreateTmpGoodRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestCreateGoodsTemporary(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCreateGoodsTemporary(
        request: CreateTmpGoodRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.createGoodsTemporary(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun deleteGoodsTemporary(
        id: Long? = null
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestDeleteGoodsTemporary(id)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestDeleteGoodsTemporary(
        id: Long? = null
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.deleteGoodsTemporary(
                id
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }


    suspend fun temporaryBatchAdd(
        request: AddTmpGoodsToCartRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestTemporaryBatchAdd(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestTemporaryBatchAdd(
        request: AddTmpGoodsToCartRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.temporaryBatchAdd(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun saveOrUpdateOpeningCash(
        request: SaveOrUpdateOpeningCashRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestSaveOrUpdateOpeningCash(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestSaveOrUpdateOpeningCash(
        request: SaveOrUpdateOpeningCashRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.saveOrUpdateOpeningCash(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun startClasses(
        request: SaveOrUpdateOpeningCashRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestStartClasses(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestStartClasses(
        request: SaveOrUpdateOpeningCashRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.shiftStart(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    //获取当前用户交接班记录
    suspend fun getShiftLog(): ApiResponse<BaseResponse<CashRegisterHandoverLogVo>> {
        return safeApiCall(
            call = {
                requestGetShiftLog()
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetShiftLog(): ApiResponse<BaseResponse<CashRegisterHandoverLogVo>> {
        val response =
            apiService.getShiftLog()
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    //分页获取交接班记录
    suspend fun pageCashRegisterHandoverLog(
        page: Int?,
        pageSize: Int?,
        keyword: String?,
        startTime: String?,
        endTime: String?,
    ): ApiResponse<BaseResponse<CashRegisterHandoverLog>> {
        return safeApiCall(
            call = {
                requestPageCashRegisterHandoverLog(page, pageSize, keyword, startTime, endTime)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestPageCashRegisterHandoverLog(
        page: Int?,
        pageSize: Int?,
        keyword: String?,
        startTime: String?,
        endTime: String?,
    ): ApiResponse<BaseResponse<CashRegisterHandoverLog>> {
        val response =
            apiService.pageCashRegisterHandoverLog(page, pageSize, keyword, startTime, endTime)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    //获取交接班记录报表
    suspend fun getShiftReportPrint(
        shiftId: Long,
    ): ApiResponse<BaseResponse<ShiftReportPrint>> {
        return safeApiCall(
            call = {
                requestGetShiftReportPrint(shiftId)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetShiftReportPrint(
        shiftId: Long,
    ): ApiResponse<BaseResponse<ShiftReportPrint>> {
        val response =
            apiService.getShiftReportPrint(shiftId)
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun uploadLog(model: UploadLogRequest): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = { requestUploadLog(model) },
            errorMessage = ""
        )
    }

    private suspend fun requestUploadLog(model: UploadLogRequest): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.uploadLog(LogRequest(content = JSON.toJSONString(model)).toRequestBody())
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code)
        }
    }


    suspend fun updateCustomerInfo(
        request: UpdateCustomerInfoRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestUpdateCustomerInfo(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestUpdateCustomerInfo(
        request: UpdateCustomerInfoRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.updateCustomerInfo(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }


    suspend fun getOrderDiscountInfoList(
        request: OrderDiscountInfoListRequest
    ): ApiResponse<List<DiscountReduceInfo>> {
        return safeApiCall(
            call = {
                requestOrderDiscountInfoList(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestOrderDiscountInfoList(
        request: OrderDiscountInfoListRequest
    ): ApiResponse<List<DiscountReduceInfo>> {
        val response =
            apiService.orderDiscountInfoList(
                request.toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun getSalesOrdersExport(
        request: SalesOrdersExportRequest
    ): ApiResponse<String> {
        return safeApiCall(
            call = {
                requestGetSalesOrdersExport(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetSalesOrdersExport(
        request: SalesOrdersExportRequest
    ): ApiResponse<String> {
        val response =
            apiService.getSalesOrdersExport(
                request.toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun getDeliveryPlatformList(
    ): ApiResponse<List<TakeOutPlatformModel>> {
        return safeApiCall(
            call = {
                requestGetDeliveryPlatformList()
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetDeliveryPlatformList(
    ): ApiResponse<List<TakeOutPlatformModel>> {
        val response =
            apiService.getDeliveryPlatformList(
            )
        return executeResponse(response)
    }

    suspend fun editDeliveryOrderNo(
        request: EditDeliveryOrderNoRequest
    ): ApiResponse<BaseBooleanResponse> {
        return safeApiCall(
            call = {
                requestEditDeliveryOrderNo(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestEditDeliveryOrderNo(
        request: EditDeliveryOrderNoRequest
    ): ApiResponse<BaseBooleanResponse> {
        val response =
            apiService.editDeliveryOrderNo(
                request.toRequestBody()
            )
        return if (response.isSuccess()) {
            ApiResponse.Success(response)
        } else {
            ApiResponse.Error(response.msg, response.code, response.getErrorCode())
        }
    }

    suspend fun updateOrderOrGoodNote(
        request: UpdateOrderOrGoodNoteRequest
    ): ApiResponse<OrderedInfoResponse> {
        return safeApiCall(
            call = {
                requestUpdateOrderOrGoodNote(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestUpdateOrderOrGoodNote(
        request: UpdateOrderOrGoodNoteRequest
    ): ApiResponse<OrderedInfoResponse> {
        val response =
            apiService.updateOrderOrGoodNote(
                request.toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun cartGoodEdit(
        request: CartGoodEditRequest
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = {
                requestCartGoodEdit(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCartGoodEdit(
        request: CartGoodEditRequest
    ): ApiResponse<CartInfoResponse> {
        val response =
            apiService.cartGoodEdit(
                request.toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun getSalesReportPrint(
        startTime: String, endTime: String, keyword: String
    ): ApiResponse<SaleReportResponse> {
        return safeApiCall(
            call = {
                requestGetSalesReportPrint(
                    startTime, endTime, keyword
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetSalesReportPrint(
        startTime: String, endTime: String, keyword: String
    ): ApiResponse<SaleReportResponse> {
        val response =
            apiService.getSalesReportPrint(
                startTime, endTime, keyword
            )
        return executeResponse(response)
    }

    suspend fun getClassificationList(
    ): ApiResponse<List<GoodClassificationModel>> {
        return safeApiCall(
            call = {
                requestGetClassificationList(
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetClassificationList(
    ): ApiResponse<List<GoodClassificationModel>> {
        val response =
            apiService.getClassificationList(
            )
        return executeResponse(response)
    }

    suspend fun getShortcutNoteList(
    ): ApiResponse<ArrayList<QuickRemarkModel>> {
        return safeApiCall(
            call = {
                requestGetShortcutNoteList(
                )
            },
            errorMessage = ""
        )
    }

    private suspend fun requestGetShortcutNoteList(
    ): ApiResponse<ArrayList<QuickRemarkModel>> {
        val response =
            apiService.getShortcutNoteList()
        return executeResponse(response)
    }


    suspend fun cartConfirmPendingGoods(
        request: CartConfirmPendingGoodsRequest
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = {
                requestCartConfirmPendingGoods(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestCartConfirmPendingGoods(
        request: CartConfirmPendingGoodsRequest
    ): ApiResponse<CartInfoResponse> {
        val response =
            apiService.cartConfirmPendingGoods(
                request.toRequestBody()
            )
        return executeResponse(response)
    }

    suspend fun asyncChange(
        request: AsyncChangeRequest
    ): ApiResponse<CartInfoResponse> {
        return safeApiCall(
            call = {
                requestAsyncChange(request)
            },
            errorMessage = ""
        )
    }

    private suspend fun requestAsyncChange(
        request: AsyncChangeRequest
    ): ApiResponse<CartInfoResponse> {
        val response =
            apiService.asyncChange(
                request.toRequestBody()
            )
        return executeResponse(response)
    }
}