package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.data.model.base.response_model.order.BaseOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.order.HeaderOrderGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.OrderMergeHeadItemBinding
import com.metathought.food_order.casheir.databinding.PendingOrderMenuItemBinding
import com.metathought.food_order.casheir.extension.addMealSetTag
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setStrokeAndColor
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.utils.SingleClickUtils
import timber.log.Timber
import androidx.core.graphics.toColorInt


/**
 * <AUTHOR>
 * @date 2024/3/2516:33
 * @description
 */
class OrderedInfoAdapter(
    val list: ArrayList<BaseOrderGoods>,
    val onItemClickListener: ((Int, OrderedGoods?) -> Unit)? = null,
    val onWarnClick: ((View, OrderedGoods) -> Unit)? = null
) : RecyclerView.Adapter<OrderedInfoAdapter.Companion.BaseViewHolder<*>>() {
    //    : RecyclerView.Adapter<OrderedInfoAdapter.OrderedInfoViewHolder>() {
    val listRefund: ArrayList<OrderedGoods> = arrayListOf()
    private var orderedInfoResponse: OrderedInfoResponse? = null

    private var selectItem: OrderedGoods? = null

    private lateinit var headerBinding: OrderMergeHeadItemBinding
    private lateinit var contentBinding: PendingOrderMenuItemBinding

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder<*> {
        return when (viewType) {
            OrderGoodViewType.MERGE_INFO.ordinal -> {
                headerBinding = OrderMergeHeadItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            OrderGoodViewType.ADD_INFO.ordinal -> {
                headerBinding = OrderMergeHeadItemBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                HeaderViewHolder(headerBinding)
            }

            OrderGoodViewType.GOOD.ordinal -> {
                contentBinding =
                    PendingOrderMenuItemBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                OrderedInfoViewHolder(contentBinding)
            }

            else -> throw IllegalArgumentException("Not a layout")
        }
    }

    override fun getItemCount() = list.size


    override fun onBindViewHolder(holder: BaseViewHolder<*>, position: Int) {
        when (holder) {
            is HeaderViewHolder -> {
                val data = (list[position] as HeaderOrderGoods)
                if (data.time != null) {
                    //加购次数显示
                    holder.binding.tvOrderId.text = data.time
                    holder.binding.tvOrderIndex.text = data.title
                    holder.binding.vLine.isVisible = false
                    if (position > 0 && !orderedInfoResponse?.mergeOrderIds.isNullOrEmpty()) {
                        /**
                         * 前一个是合并title 的情况下
                         */
                        holder.binding.tvOrderId.textSize = 12f
                        holder.binding.tvOrderId.setTextColor(holder.itemView.context.getColor(R.color.black60))
                        holder.binding.tvOrderIndex.textSize = 12f
                        holder.binding.tvOrderIndex.setTextColor(holder.itemView.context.getColor(R.color.black60))
                        if (list[position - 1] is OrderedGoods) {
                            holder.binding.vLine.isInvisible = true
                        } else {
                            holder.binding.vLine.isVisible = false
                        }
                    } else {
                        holder.binding.tvOrderId.textSize = 14f
                        holder.binding.tvOrderId.setTextColor(Color.BLACK)
                        holder.binding.tvOrderIndex.textSize = 14f
                        holder.binding.tvOrderIndex.setTextColor(Color.BLACK)
                        if (position == 0) {
                            holder.binding.vLine.isVisible = false
                        } else {
                            holder.binding.vLine.isInvisible = true
                        }
                    }
                } else {
                    /**
                     * 合并title
                     ***/
                    holder.binding.tvOrderId.text = data.orderId
                    holder.binding.tvOrderIndex.text = data.title
                    holder.binding.vLine.isVisible = position != 0
                    if (position + 1 < list.size) {
                        holder.binding.tvNoGoods.isVisible =
                            list[position + 1] is HeaderOrderGoods && (list[position + 1] as HeaderOrderGoods).time == null
                    } else if (position == list.size - 1) {
                        holder.binding.tvNoGoods.isVisible = list[position] is HeaderOrderGoods
                    } else {
                        holder.binding.tvNoGoods.isVisible = false
                    }
                }
            }

            is OrderedInfoViewHolder -> {
                (list[position] is OrderedGoods).let {
                    Timber.e("OrderedInfoViewHolder  33333")
                    holder.bind(list[position] as OrderedGoods, position)
                }
            }
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(
        newData: ArrayList<BaseOrderGoods>?,
        refundData: ArrayList<OrderedGoods>? = null,
        orderedInfoResponse: OrderedInfoResponse? = null
    ) {
        if (newData != null) {
            this.orderedInfoResponse = orderedInfoResponse
            this.list.clear()
            this.list.addAll(newData)
            this.listRefund.clear()
            refundData?.let {
                this.listRefund.addAll(it)
            }

            if (selectItem != null) {
                val index = this.list.indexOfFirst {
                    it is OrderedGoods && it.getHash() == selectItem?.getHash()
                }
                if (index == -1) {
                    Timber.e("选中的在菜单列表没有 ，要清掉选中的")
                    selectItem = null
                }
            }

            notifyDataSetChanged()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun clearSelect() {
        selectItem = null
        notifyDataSetChanged()
    }

    fun getSelectItem(): OrderedGoods? {
        return selectItem
    }

    override fun getItemViewType(position: Int): Int {
        return when (list[position].type) {
            "merge_info" -> OrderGoodViewType.MERGE_INFO.ordinal
            "add_info" -> OrderGoodViewType.ADD_INFO.ordinal
//            "good" -> OrderGoodViewType.GOOD.ordinal
            else -> {
                OrderGoodViewType.GOOD.ordinal
            }
        }
    }

    inner class HeaderViewHolder(val binding: OrderMergeHeadItemBinding) :
        BaseViewHolder<OrderMergeHeadItemBinding>(binding.root) {
    }

    @SuppressLint("NotifyDataSetChanged")
    inner class OrderedInfoViewHolder(val binding: PendingOrderMenuItemBinding) :
        BaseViewHolder<PendingOrderMenuItemBinding>(binding.root) {

        init {
            binding.apply {
                binding.layoutBg.setOnClickListener {
                    bindingAdapterPosition.let {
                        if (it != -1) {
                            if (layoutPosition != -1 && onItemClickListener != null) {
                                val data = list[layoutPosition]
                                if (data is OrderedGoods) {
                                    if (data != selectItem) {
                                        selectItem = data
                                        onItemClickListener?.invoke(layoutPosition, data)
                                        notifyDataSetChanged()
                                    } else {
                                        clearSelect()
                                        onItemClickListener?.invoke(layoutPosition, null)

                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        @SuppressLint("SetTextI18n")
        fun bind(resource: OrderedGoods, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        layoutPrice.ivWarn.isVisible =
                            it.isSetSingleItemDiscount()
                        layoutPrice.ivWarn.setOnClickListener {
                            SingleClickUtils.isFastDoubleClick {
                                onWarnClick?.invoke(layoutPrice.ivWarn, resource)

                            }
                        }

                        if (selectItem != null && it.getHash() == selectItem?.getHash()) {
                            selectItem = it
                            layoutBg.isSelected = true
                        } else {
                            layoutBg.isSelected = false
                        }

                        tvRefundMinusCount.isVisible = false
                        layoutPrice.tvRefundAmount.isVisible = false

                        tvFoodName.text = it.name
                        if (!resource.orderMealSetGoodsDTOList.isNullOrEmpty()) {
                            tvFoodName.addMealSetTag(itemView.context)
                        }

                        val activityLabel = it.activityLabels?.firstOrNull()
                        if (activityLabel != null && orderedInfoResponse?.isTakeOut() != true) {
                            tvDiscountActivity.setStrokeAndColor(
                                color = activityLabel.color.toColorInt()
                            )
                            tvDiscountActivity.setTextColor(activityLabel.color.toColorInt())
                            tvDiscountActivity.text = activityLabel.name
                            tvDiscountActivity.isVisible = true
                        } else {
                            tvDiscountActivity.isVisible = false
                        }

                        if (it.goodsType == GoodTypeEnum.TEMPORARY.id) {
                            tvTmpSign.setStrokeAndColor(color = R.color.black60)
                            tvTmpSign.isVisible = true
                        } else {
                            tvTmpSign.isVisible = false
                        }

                        rvMealGoodsList.isVisible = !it.orderMealSetGoodsDTOList.isNullOrEmpty()
                        rvMealGoodsList.adapter =
                            OrderMealSetGoodsAdapter(it.orderMealSetGoodsDTOList ?: emptyList())

                        val goodsTag = it.getGoodsTagStrNotMealSet()
                        tvFoodSubName.isVisible = goodsTag.isNotEmpty()
                        tvFoodSubName.text = goodsTag
                        tvFoodCount.text = it.num.toString()

                        layoutPrice.tvVipPrice.text =
                            it.totalVipPriceWithSingleDiscount().priceFormatTwoDigitZero2()

                        //是否使用会员价
                        val isUseDiscount = orderedInfoResponse?.isUseDiscount == true
                        val isShowVipPrice = resource.isShowVipPrice()
                        tvRemark.isVisible = it.getFinalNote().isNotEmpty()
                        tvRemark.text = "${getString(R.string.remark)} : ${it.getFinalNote()}"

                        layoutPrice.tvVipPrice.isVisible = isShowVipPrice
                        layoutPrice.tvWeight.isVisible = false
                        layoutPrice.tvTimePriceSign.isVisible = false

                        if (it.isToBeWeighed()) {
                            //如果是称重商品
                            if (it.isHasCompleteWeight()) {
                                Timber.e("已称重")
                                layoutPrice.tvWeight.isVisible = true
                                layoutPrice.tvWeight.text = "(${it.getWeightStr()})"
                                if (it.isMealSet()) {
                                    layoutPrice.tvWeight.isVisible = false
                                }
                            } else {
                                Timber.e("未称重")
                                layoutPrice.tvFoodPrice.text = getString(R.string.to_be_weighed)
                                layoutPrice.tvVipPrice.isVisible = false
                            }
                        }

                        if (it.isTimePriceGood()) {
                            //如果是时价菜
                            if (!it.isHasCompletePricing()) {
                                layoutPrice.tvFoodPrice.text = getString(R.string.time_price)
                            } else {
                                layoutPrice.tvTimePriceSign.isVisible = true
                            }
                        }

                        if (it.isHasProcessed()) {
                            if (isUseDiscount) {
                                if (resource.isShowVipPrice()) {
                                    layoutPrice.tvFoodPrice.text =
                                        resource.totalVipPriceWithSingleDiscount()
                                            .priceFormatTwoDigitZero2()
                                } else {
                                    layoutPrice.tvFoodPrice.text =
                                        resource.totalPriceWithSingleDiscount()
                                            .priceFormatTwoDigitZero2()
                                }
                            } else {
                                layoutPrice.tvFoodPrice.text = FoundationHelper.getPriceStrByUnit(
                                    orderedInfoResponse?.conversionRatio
                                        ?: FoundationHelper.conversionRatio,
                                    resource.totalPriceWithSingleDiscount(),
                                    orderedInfoResponse?.isKhr() == true
                                )
                            }
                        }

                        listRefund.forEach {
                            if (it.getHash() == resource.getHash()) {
                                if ((it.num ?: 0) > 0) {
                                    tvRefundMinusCount.isVisible = true
                                    tvRefundMinusCount.text = "-${it.num.toString()}"
                                    layoutPrice.tvRefundAmount.isVisible = true

                                    layoutPrice.tvRefundAmount.text =
                                        "-${
                                            FoundationHelper.getPriceStrByUnit(
                                                orderedInfoResponse?.conversionRatio
                                                    ?: FoundationHelper.conversionRatio,
                                                it.totalRefundReducePrice(true),
                                                orderedInfoResponse?.isKhr() == true
                                            )
                                        }"
                                    //部分退款
                                    layoutPrice.tvVipPrice.isVisible = false
                                }
                            }
                        }


                        if (orderedInfoResponse != null) {
                            /**
                             * 1.已支付/已预定/全额退款——只显示实付金额
                             * 2.部分退款——只显示实付金额/实退金额
                             * 3.待确认/已确认/待支付——显示原价和会员价
                             * **/
//                            layoutPrice.llFoodPrice.isEnabled = false
                            when (orderedInfoResponse?.payStatus) {
                                OrderedStatusEnum.CANCEL_ORDER.id,
                                OrderedStatusEnum.PARTIAL_REFUND.id,
                                OrderedStatusEnum.PAID.id,
                                OrderedStatusEnum.PREORDER.id,
                                OrderedStatusEnum.CREDIT_UNPAID.id,
                                OrderedStatusEnum.CREDIT_PAID.id,
                                OrderedStatusEnum.FULL_REFUND.id -> {
                                    layoutPrice.tvVipPrice.isVisible = false
                                }

                                OrderedStatusEnum.UNPAID.id,
                                OrderedStatusEnum.TO_BE_CONFIRM.id,
                                OrderedStatusEnum.BE_CONFIRM.id -> {
//                                    if (it.isToBeWeighed()) {
//                                        layoutPrice.llFoodPrice.isEnabled = true
//                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    companion object {
        abstract class BaseViewHolder<T>(view: View) : RecyclerView.ViewHolder(view)
    }

    //    enum class ViewType {
//        Header,
//        Content
//    }
    enum class OrderGoodViewType {
        MERGE_INFO,
        ADD_INFO,
        GOOD
    }
}