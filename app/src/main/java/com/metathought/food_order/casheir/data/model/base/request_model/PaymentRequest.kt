package com.metathought.food_order.casheir.data.model.base.request_model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

/**
 * <AUTHOR>
 * @date 2024/3/2114:37
 * @description
 */
data class PaymentReserveRequest(
    var diningStyle: Int?,
    var tableUuid: String?,
    var plan: Boolean,
    //支付类型: 1-线上支付; 2-现金支付; 3-用户余额支付;
    //Payment type: 1-online payment; 2-cash payment; 3-user balance payment;
    var payType: Int,
    //会员账户id（consumer_pay_account表id）
    //Member account id
    var accountId: String? = null,
    var note: String = "",
    var goodsJson: GoodsReserveJsonVo,
    var customerInfoVo: CustomerInfoVo
)


data class GoodsReserveJsonVo(
    var peopleDate: String?,
    var peopleNum: Int?,
    var serviceCharge: Long?,
    var totalPrice: Long?,
    var goodsTotalNum: Int?,
    var goodsReserveVoList: List<GoodsRequest>?
) {


    fun getSubtotal(): Long {
        return (totalPrice ?: 0) - (serviceCharge ?: 0)
    }

}

data class PaymentRequest(
    //就餐方式:0-堂食  1-外带  2-预定  3-外卖
    var diningStyle: Int?,
    var tableUuid: String?,
    //支付类型: 1-线上支付; 2-现金支付; 3-用户余额支付;
    //Payment type: 1-online payment; 2-cash payment; 3-user balance payment;
    var payType: Int? = null,
    //会员账户id（consumer_pay_account表id）
    //Member account id
    var accountId: String? = null,
    //混合支付 传的余额支付多少
    var balancePayAmount: BigDecimal? = null,
    var note: String = "",
    var isPosPrint: Boolean = true,
//    var isReservation: Boolean,
    var isPreOrder: Boolean?,
//    var goodsJson: GoodsJsonVo,
    var goodsList: List<GoodsBo>,
    var customerInfoVo: CustomerInfoVo? = null,
    //Pre-order only required
    var peopleDate: String? = null,
    var peopleNum: Int? = null,
    //线下支付渠道ID Offline payment channel ID
    var offlinePayChannelsId: Int? = null,
    //线下收款渠道名  Offline payment channel name
    var offlinePayChannelsName: String? = null,
    //收取现金 现金支付时使用,瑞尔 Cash payment is accepted when using KHR
    var collectCash: Long? = null,
    //找零金额 现金支付时使用，瑞尔 Change amount is used when paying in cash, KHR
    var changeAmount: Long? = null,
    //收取现金 现金支付时使用,美元 Cash Payment: USD
    var collectCashDollar: BigDecimal? = null,
    //找零金额 现金支付时使用，美元 Change amount used when paying in cash, USD
    var changeAmountDollar: Double? = null,


    //@Schema(description = "类型 0:默认 1.销售价 2.会员 3:销售价+会员价 4.配置折扣活动 5.USD自定义整单减免 6.KHR自定义整单减免")
    var reduceType: Int? = null,
    //减免百分比
    val reduceRate: Double? = null,
    //减免金额（美元）
    val reduceDollar: BigDecimal? = null,
    //减免vip金额（美元）
    val reduceVipDollar: BigDecimal? = null,
    //减免金额（khr）
    val reduceKhr: BigDecimal? = null,
    //减免vip金额（khr）
    val reduceVipKhr: BigDecimal? = null,

    //减免原因：0：discount(正常折扣) 1:void(菜品有问题时原因必填写)
    val discountType: Int? = null,
    //原因
    var reduceReason: String? = null,
    //折扣id
    val discountReduceActivityId: String? = null,


    //优惠券码
    var couponCode: String? = null,

    //订单号
    var orderNo: String? = null,

    //单品减免原因
    var singleReduceReason: String? = null,

    //就餐方式为外卖，外卖平台id要有值
    var deliveryPlatformId: String? = null,

    //外卖订单号
    var deliveryOrderNo: String? = null,

    var consumerPayRegisterInfo: ConsumerPayRegisterInfo? = null,//会员注册信息

    var isCredit: Boolean = false,    //是否挂账

    var creditReason: String? = null,//挂账原因

)

data class ConsumerPayRegisterInfo(
    var telephone: String? = null,    //电话<账号>
    var nickName: String? = null, //用户昵称
)

@Parcelize
data class GoodsBo(
    var id: String?,
    var num: Int?,

    var tagItemId: String?,
    var feeds: List<FeedBo>?,
    var pricingMethod: Int?,
    var weight: Double?,
    var uuid: String?,
    var discountPrice: Long?,
    //商品类型:0-普通商品， 1-临时商品"
    var goodsType: Int?,
    /**
     * 单品减免
     */
    var singleDiscountGoods: SingleDiscountGoods? = null,

    /**
     * 套餐内容
     */
    var mealSetGoodsDTOList: List<MealSetGood>? = null,

    /**
     * 请求给服务端的单品折扣参数
     */
    var singleItemDiscount: SingleDiscountRequest? = null,

    /**
     * 请求给服务端的本地生成的hashKey
     */
    var goodsHashKey: String?,
    /**
     * 备注
     */
    var note: String?,

    /**
     * pricingCompleted sellPrice vipPrice  时价菜才传
     */
    var pricingCompleted: Boolean? = null,
    var sellPrice: Long? = null,
    var vipPrice: Long? = null,

    //本地用的
    var isProcessed: Boolean? = false

) : Parcelable {


}

@Parcelize
data class FeedBo(
    var id: String?,
    var num: Int?
) : Parcelable

//套餐请求的model
@Parcelize
data class MealSetGood(
    var mealSetGroupId: String?,
    var mealSetGoodsId: String?,
    var number: Int?,
    val tagItemIds: String?,

    //TODO 2.16.10 新增字段
    //商品重量(称重商品使用)
    val weight: Double?,
    //商品唯一标识(称重商品使用)
    val uuid: String?,
) : Parcelable

data class GoodsJsonVo(
    var peopleDate: String?,
    var peopleNum: Int?,
    var serviceCharge: Long?,
    var totalPrice: Long?,
    var goodsTotalNum: Int?,
    var goodsList: List<GoodsRequest>?
) {
    fun getSubtotal(): Long {
        return (totalPrice ?: 0) - (serviceCharge ?: 0)
    }
}

data class CustomerInfoVo(
    var name: String?,
    var areaCode: String?,
    var mobile: String?,
    var diningNumber: Int?,
    var diningTime: String?
) {

    fun getMobilePhone(): String {
        return areaCode + mobile
    }

    fun getMobilePhoneDisplay(): String {
        return "+$areaCode $mobile"
    }
}


data class PendingRequest(
    var diningStyle: Int?,
    /**
     * 存单备注
     */
    var note: String? = "",
//    var isReservation: Boolean,
    var isPreOrder: Boolean,
    var goodsList: List<GoodsBo>,
    var customerInfoVo: CustomerInfoVo,
    var serialNumber: String?,
    /**
     * 订单的备注
     */
    var orderNote: String? = "",

    var tableUuid: String? = null
)