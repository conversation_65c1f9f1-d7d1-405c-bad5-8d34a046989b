package com.metathought.food_order.casheir.ui.table.occupied

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.google.gson.Gson
import com.metathought.food_order.casheir.data.model.base.response_model.order.Customer
import com.metathought.food_order.casheir.data.model.base.response_model.table.TableResponseItem
import com.metathought.food_order.casheir.databinding.DialogOccupiedBinding
import com.metathought.food_order.casheir.databinding.DialogSharedOccupiedBinding


class SharedOccupiedTableDialog : DialogFragment() {
    private var binding: DialogSharedOccupiedBinding? = null
    private var menuButtonListener: (() -> Unit)? = null
    private var viewOrderedButtonListener: (() -> Unit)? = null
    private var printerButtonListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogSharedOccupiedBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
    }

    override fun onResume() {
        super.onResume()
//        context?.let {
//            val displayMetrics = getDisplayMetrics(it)
//            val screenHeight = (displayMetrics.heightPixels * PERCENT_70).toInt()
//            val screenWidth = (displayMetrics.widthPixels * PERCENT_70).toInt()
//            dialog?.window?.setLayout(screenWidth, screenHeight)
//        }
    }

    private fun initData() {
        val content = Gson().fromJson(arguments?.getString(CONTENT), TableResponseItem::class.java)
        binding?.apply {
            tvPeople.text = "0/${content.maxPeopleCount}"
            val customer =
                Gson().fromJson<Customer>(content.customerJson, Customer::class.java)
            customer?.let {
                tvPeople.text = "${customer.diningNumber}/${content.maxPeopleCount}"
            }

            tvTableID.text = content.name
        }
    }

    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }

            btnViewOrder.setOnClickListener {
                viewOrderedButtonListener?.invoke()
                dismissAllowingStateLoss()
            }

            btnOrder.setOnClickListener {
                menuButtonListener?.invoke()
                dismissAllowingStateLoss()
            }

            cardPrinter.setOnClickListener {
                //打印临时桌码 Print temporary table code
                printerButtonListener?.invoke()
                dismissAllowingStateLoss()
            }
        }
    }

    companion object {
        //        private const val RESERVE_DIALOG = "RESERVE_DIALOG"
        private const val CONTENT = "CONTENT"
        private const val TAG = "SharedOccupiedTableDial"


        // start - viettran1 - AM -133 - 18/07/2023
        private const val PERCENT_70 = 0.7
        private const val PERCENT_85 = 0.85

        // end - viettran1 - AM -133 - 18/07/2023
        fun showDialog(
            fragmentManager: FragmentManager,
            content: String? = null,
            menuButtonListener: (() -> Unit),
            viewOrderedButtonListener: (() -> Unit),
            printerButtonListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return
            fragment = newInstance(
                menuButtonListener = menuButtonListener,
                iCancelListener = viewOrderedButtonListener,
                iPrinterListener = printerButtonListener,
                content = content
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? SharedOccupiedTableDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            iCancelListener: (() -> Unit),
            menuButtonListener: (() -> Unit),
            iPrinterListener: (() -> Unit),
            content: String? = null
        ): SharedOccupiedTableDialog {
            val args = Bundle()
            args.putString(CONTENT, content)
            val fragment = SharedOccupiedTableDialog()
            fragment.menuButtonListener = menuButtonListener
            fragment.viewOrderedButtonListener = iCancelListener
            fragment.printerButtonListener = iPrinterListener
            fragment.arguments = args
            return fragment
        }
    }
//
//
//    private fun getDisplayMetrics(context: Context): DisplayMetrics {
//        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
//        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
//        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
//        return defaultDisplayContext.resources.displayMetrics
//    }
}
