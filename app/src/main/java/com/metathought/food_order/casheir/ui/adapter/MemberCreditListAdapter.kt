package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.member.CreditRecord
import com.metathought.food_order.casheir.databinding.ItemMemberCreditBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero3
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero4


class MemberCreditListAdapter(
    val list: ArrayList<CreditRecord>,
    val onDetailClickListener: (CreditRecord) -> Unit,
    val onReceivePaymentClickListener: (CreditRecord) -> Unit,
) : RecyclerView.Adapter<MemberCreditListAdapter.BalanceListViewHolder>() {


    inner class BalanceListViewHolder(val binding: ItemMemberCreditBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: CreditRecord, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
                        tvAccountName.text =
                            if (!it.nickName.isNullOrEmpty()) it.nickName
                            else it.telephone?.takeLast(4)
                        tvAccountNumber.text = it.telephone
                        tvCreditAmount.text = it.amount?.priceFormatTwoDigitZero4()
                        tvReceivePayment.setOnClickListener {
                            onReceivePaymentClickListener.invoke(resource)
                        }
                        tvDetail.setOnClickListener {
                            onDetailClickListener.invoke(resource)
                        }
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BalanceListViewHolder {
        val itemView =
            ItemMemberCreditBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BalanceListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: BalanceListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    @SuppressLint("NotifyDataSetChanged")
    fun replaceData(list: List<CreditRecord>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: List<CreditRecord>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


}