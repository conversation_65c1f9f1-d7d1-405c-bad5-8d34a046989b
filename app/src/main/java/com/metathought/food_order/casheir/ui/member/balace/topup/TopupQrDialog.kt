package com.metathought.food_order.casheir.ui.member.balace.topup

import android.content.Context
import android.content.DialogInterface
import android.hardware.display.DisplayManager
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.viewModels
import com.github.alexzhirkevich.customqrgenerator.QrData
import com.github.alexzhirkevich.customqrgenerator.vector.QrCodeDrawable
import com.github.alexzhirkevich.customqrgenerator.vector.QrVectorOptions
import com.github.alexzhirkevich.customqrgenerator.vector.style.QrVectorLogo
import com.github.alexzhirkevich.customqrgenerator.vector.style.QrVectorLogoPadding
import com.github.alexzhirkevich.customqrgenerator.vector.style.QrVectorLogoShape
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargePaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.RechargeQRStatusResponse
import com.metathought.food_order.casheir.databinding.DialogPaymentQrBinding
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import dagger.hilt.android.AndroidEntryPoint
import kh.org.nbc.bakong_khqr.BakongKHQR
import java.nio.charset.Charset

/**
 * <AUTHOR>
 * @date 2024/3/2115:45
 * @description
 */
@AndroidEntryPoint
class TopupQrDialog : DialogFragment() {
    //    private var blackBoard: PaymentScreenUI? = null
    private var blackBoard: SecondaryScreenUI? = null
    private var binding: DialogPaymentQrBinding? = null
    private lateinit var qrData: RechargePaymentResponse
    private val paymentQrViewModel: TopupQrViewModel by viewModels()
    private var successListener: ((RechargeQRStatusResponse) -> Unit)? = null
    private var onCloseListener: (() -> Unit)? = null
    private var isPaySuccess = false

    companion object {
        private const val KEY_PAYMENT_QR = "KEY_PAYMENT_QR"
        private const val KEY_PAYMENT_RES = "KEY_PAYMENT_RES"

        fun showDialog(
            fragmentManager: FragmentManager,
            paymentResponse: RechargePaymentResponse,
            blackBoard: SecondaryScreenUI?,
            successListener: ((RechargeQRStatusResponse) -> Unit),
            onCloseListener: (() -> Unit),
        ) {
            var fragment = fragmentManager.findFragmentByTag(KEY_PAYMENT_QR)
            if (fragment != null) return
            fragment = newInstance(paymentResponse, blackBoard, successListener, onCloseListener)
            fragment.show(fragmentManager, KEY_PAYMENT_QR)
        }

        private fun newInstance(
            paymentResponse: RechargePaymentResponse,
            blackBoard: SecondaryScreenUI?,
            callback: ((RechargeQRStatusResponse) -> Unit),
            onCloseListener: (() -> Unit),
        ): TopupQrDialog {
            return TopupQrDialog().apply {
                successListener = callback
                this.blackBoard = blackBoard
                this.onCloseListener = onCloseListener
                arguments = Bundle().apply {
                    putParcelable(KEY_PAYMENT_RES, paymentResponse)
                }
            }
        }

        fun getQROption(logo: Int, context: Context) = QrVectorOptions.Builder()
            .setPadding(.0f)
            .setLogo(
                QrVectorLogo(
                    drawable = ContextCompat
                        .getDrawable(context, logo),
                    size = .20f,
                    padding = QrVectorLogoPadding.Natural(.2f),
                    shape = QrVectorLogoShape
                        .Circle
                )
            ).build()
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.8).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.4).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun getDisplayMetrics(context: Context): DisplayMetrics {
        val displayManager = context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val defaultDisplay = displayManager.getDisplay(Display.DEFAULT_DISPLAY)
        val defaultDisplayContext = context.createDisplayContext(defaultDisplay)
        return defaultDisplayContext.resources.displayMetrics
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogPaymentQrBinding.inflate(layoutInflater)
        return binding?.root
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initView()
        initListener()
        initObserver()
    }


    private fun initListener() {
        binding?.run {
            btnClose.setOnClickListener {
//                onCloseListener?.invoke()
//                blackBoard?.showAd()
//                SecondaryManager.showDefault()
                dismissAllowingStateLoss()
            }
        }
    }


    private fun initObserver() {
        paymentQrViewModel.uiState.observe(viewLifecycleOwner) { state ->
            state.error?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
//                blackBoard?.showAd()
//                SecondaryManager.showDefault()
                dismissAllowingStateLoss()
            }
            state.isExpire?.let {
                Toast.makeText(context, getString(R.string.order_expired), Toast.LENGTH_SHORT)
                    .show()
//                blackBoard?.showAd()
//                SecondaryManager.showDefault()
                dismissAllowingStateLoss()
            }
            state.time?.let {
                binding?.run {
                    tvTime.text = it
//                    blackBoard?.updateTime(it)
                }
            }
            state.success?.let {
                binding?.apply {
                    blackBoard?.showPaymentTopUpResult(qrData)
                }
                isPaySuccess = true
                successListener?.invoke(it)
//                blackBoard?.showAd()
                dismissAllowingStateLoss()
            }
        }
    }


    private fun initView() {
        qrData = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelable(KEY_PAYMENT_RES, RechargePaymentResponse::class.java)!!
        } else {
            arguments?.getParcelable(KEY_PAYMENT_RES)!!
        }
        binding?.run {
            paymentQrViewModel.initQrData(qrData)
            val decode = BakongKHQR.decode(qrData.qrCode ?: "")
            val amountStr = "$${decode.data.transactionAmount}"
            tvScanQRAmount.text = amountStr
            tvScanQRName.text = MainDashboardFragment.CURRENT_USER?.getStoreNameByLan()
            imgQR.setImageDrawable(
                QrCodeDrawable(
                    QrData.Url(qrData.qrCode ?: ""),
                    getQROption(R.mipmap.icon_qr_bakong, requireContext()),
                    Charset.forName("UTF-8")
                )
            )
            initializeBlackBoard(qrData)
        }


    }

    private fun initializeBlackBoard(paymentResponse: RechargePaymentResponse) {
        context?.let {
//            val displayManager = it.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager?
//            val displays =
//                displayManager!!.getDisplays(DisplayManager.DISPLAY_CATEGORY_PRESENTATION)
//            if (displays.isNotEmpty()) {
//                blackBoard = SecondaryScreenUI(it, displays[0])
//                blackBoard?.paymentResponse = PaymentResponse(orderNo = paymentResponse.orderId, qrcode = paymentResponse.qrCode, 0,0)
//                blackBoard?.isShowQR = true
//                blackBoard?.show()
//            }
//            blackBoard = SecondaryManager.showPaymentSecond(
//                it,
//                PaymentResponse(
//                    orderNo = paymentResponse.orderId,
//                    qrcode = paymentResponse.qrCode,
//                    0,
//                    0
//                )
//            )
            binding?.apply {
                blackBoard?.showTopUpQRPayment(paymentResponse)
            }

        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        if (!isPaySuccess) {
            onCloseListener?.invoke()
        }

        super.onDismiss(dialog)

    }
}