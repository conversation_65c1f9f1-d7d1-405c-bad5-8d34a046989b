package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedGoods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemTicketOrderMenuBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import timber.log.Timber
import java.util.Locale


/**
 * 堂食/外带/预约 打印小票菜品信息
 * Dine-in/Take-away/Booking to print receipt information
 * <AUTHOR>
 * @date 2024/5/1014:15
 * @description
 */
class PrinterTicketMenuAdapter(
    val list: ArrayList<OrderedGoods>,
    val currentOrderedInfo: OrderedInfoResponse?,
    val isKitchen: Boolean = false,
    val templateItem: PrintTamplateResponseItem,
    val isEightyWidth: Boolean?,  //是否80打印机
    val isCancelGoods: Boolean = false //是否退菜

) :
    RecyclerView.Adapter<PrinterTicketMenuAdapter.PrinterTicketMenuViewHolder>() {

    inner class PrinterTicketMenuViewHolder(val binding: ItemTicketOrderMenuBinding) :
        RecyclerView.ViewHolder(binding.root) {

        @SuppressLint("SetTextI18n")
        fun bind(resource: OrderedGoods, position: Int) {
            itemView.context.run {
                resource.let {
                    binding.apply {
                        val localeList = mutableListOf<Locale>()
                        val langList = templateItem.getLangList()
                        langList.forEach {
                            localeList.add(Locale(it.uppercase()))
                        }
                        if (localeList.isEmpty()) {
                            localeList.add(Locale("EN"))
                        }
                        var nameString = ""
                        localeList.forEachIndexed { index, lang ->
                            val foodName =
                                it.getNameByLocal(lang)
                            Timber.e("foodName -> ${foodName}     lang:${lang.language}")
                            if (isEightyWidth == true) {
                                //80的用这个
                                if (index == 0) {
                                    tvFoodNameEn.text = foodName
                                    tvFoodNameEn.isVisible = !foodName.isNullOrEmpty()
                                } else if (index == 1) {
                                    tvFoodNameKh.text = foodName
                                    tvFoodNameKh.isGone =
                                        tvFoodNameKh.text == tvFoodNameEn.text || tvFoodNameKh.text.isNullOrEmpty()
                                    tvFoodNameKh.setPadding(60, 0, 0, 0)
                                }
                            } else {
                                //58的用这个
                                if (nameString.isEmpty()) {
                                    nameString = foodName ?: ""
                                } else {
                                    if (nameString != foodName) {
                                        nameString = "$nameString $foodName"
                                    }
                                }
                            }
                        }

                        if (isEightyWidth == true) {
                            //80的用这个
                            //如果菜名一样就隐藏一个
                            llFoodName.isVisible = true
                            tvFoodName.isVisible = false
                            llItemIndex.isVisible = true
                            llFoodDiscountPrice.isVisible = true

                            (llItemIndex.layoutParams as LinearLayout.LayoutParams).weight = 1f
                            (llFoodName.layoutParams as LinearLayout.LayoutParams).weight = 2.5f
                            (llFoodCount.layoutParams as LinearLayout.LayoutParams).weight = 1f
                            (llFoodPrice.layoutParams as LinearLayout.LayoutParams).weight = 1.5f
                            (llFoodDiscountPrice.layoutParams as LinearLayout.LayoutParams).weight =
                                1.5f
                            (llFoodTotalPrice.layoutParams as LinearLayout.LayoutParams).weight =
                                1.5f

                            if (it.isToBeWeighed() && it.isHasProcessed() && !it.isMealSet()) {
                                tvFoodNameEn.text =
                                    "${tvFoodNameEn.text}(${it.getWeightStr()})"
                            }

                            tvIndex.text = "${position + 1}"
                        } else {
                            //58的用这个
                            if (it.isToBeWeighed() && it.isHasProcessed() && !it.isMealSet()) {
                                nameString =
                                    "${nameString}(${it.getWeightStr()})"
                            }
                            tvFoodName.text = nameString
                        }


                        specRecyclerView.isGone = it.tagItems.isNullOrEmpty()
                        specRecyclerView.adapter =
                            PrinterSpecificationsAdapter(
                                it.tagItems ?: arrayListOf(),
                                resource,
                                isKitchen,
                                templateItem,
                                isEightyWidth,
                                if (isCancelGoods) false else it.isPrintShowTagItemPrice(orderInfo = currentOrderedInfo)
                            )

                        feedRecyclerView.isGone = it.feeds.isNullOrEmpty()
                        feedRecyclerView.adapter =
                            PrinterFeedAdapter(
                                it.feeds ?: arrayListOf(),
                                isKitchen,
                                templateItem,
                                isEightyWidth,
                                if (isCancelGoods) false else it.isPrintShowTagItemPrice(orderInfo = currentOrderedInfo)
                            )

                        setMealRecyclerView.isGone = it.orderMealSetGoodsDTOList.isNullOrEmpty()
                        setMealRecyclerView.adapter =
                            PrinterSetMealGoodAdapter(
                                it.orderMealSetGoodsDTOList ?: arrayListOf(),
                                isKitchen,
                                templateItem,
                                isEightyWidth,
                                if (isCancelGoods) false else it.isPrintShowTagItemPrice(orderInfo = currentOrderedInfo)
                            )

                        tvFoodCount.text = "x${it.num.toString()}"

                        if (currentOrderedInfo?.isUseDiscount == true) {
                            if (resource.isShowVipPrice()) {
                                //如果vip 价格有效
                                tvFoodPrice.text =
                                    resource.getTicketSingleVipPrice().priceFormatTwoDigitZero2()

                                tvFoodTotalPrice.text = resource.totalVipPriceWithSingleDiscount()
                                    .priceFormatTwoDigitZero2()

                                tvFoodDiscountPrice.text =
                                    resource.calculateSingleDiscountVipPrice()
                                        .priceFormatTwoDigitZero2()
                                tvFoodOrginalTotalPrice.isVisible =
                                    resource.totalVipPriceWithSingleDiscount() != resource.totalPrice()
                                tvFoodOrginalTotalPrice.text = resource.totalVipPrice()
                                    .priceFormatTwoDigitZero2()
                            } else {
                                tvFoodPrice.text =
                                    resource.getTicketSinglePrice().priceFormatTwoDigitZero2()
                                tvFoodTotalPrice.text =
                                    resource.totalPriceWithSingleDiscount()
                                        .priceFormatTwoDigitZero2()
                                tvFoodDiscountPrice.text = resource.calculateSingleDiscountPrice()
                                    .priceFormatTwoDigitZero2()
                                tvFoodOrginalTotalPrice.isVisible =
                                    resource.totalPriceWithSingleDiscount() != resource.totalPrice()
                                tvFoodOrginalTotalPrice.text = resource.totalPrice()
                                    .priceFormatTwoDigitZero2()
                            }
                        } else {
                            tvFoodPrice.text =
                                "${resource.getTicketSinglePrice()?.priceFormatTwoDigitZero2()}"
                            tvFoodDiscountPrice.text = resource.calculateSingleDiscountPrice()
                                .priceFormatTwoDigitZero2()
                            tvFoodTotalPrice.text =
                                resource.totalPriceWithSingleDiscount()
                                    .priceFormatTwoDigitZero2()
                            tvFoodOrginalTotalPrice.isVisible =
                                resource.totalPriceWithSingleDiscount() != resource.totalPrice()
                            tvFoodOrginalTotalPrice.text = resource.totalPrice()
                                .priceFormatTwoDigitZero2()

                        }

                        if (isCancelGoods) {
                            tvFoodPrice.isVisible = false
                            tvFoodTotalPrice.isVisible = false
                            tvFoodDiscountPrice.isVisible = false
                        }

                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        PrinterTicketMenuViewHolder(
            ItemTicketOrderMenuBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(holder: PrinterTicketMenuViewHolder, position: Int) {
        holder.bind(list[position], position)
    }

    fun replaceData(
        newData: ArrayList<OrderedGoods>
    ) {
        if (newData.isNotEmpty()) {
            list.clear()
            list.addAll(newData)
            notifyDataSetChanged()
        }
    }

}