package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.util.AttributeSet
import android.util.Log
import androidx.appcompat.widget.AppCompatTextView
import java.util.Timer
import java.util.TimerTask


/**
 *<AUTHOR>
 *@time  2024/11/1
 *@desc
 **/

class AlwaysMarqueeTextView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null
) :
    AppCompatTextView(context!!, attrs) {
    private var mOffsetX = 0
    private val mRect = Rect()
    private var mText = ""
    private var mTimer: Timer?
    private var mTimerTask: TimerTask?
    private var mTextWidth = 0

    private var mSpeed = 2

    init {
        mTimer = Timer()
        mTimerTask = MyTimerTask()
        //更新帧率24
        mTimer!!.schedule(mTimerTask, 0, (1000 / PFS).toLong())
    }

    fun setMarqueeText(text: String) {
        setText(text)
        mText = text.toString()
        val textPaint = paint
        textPaint.getTextBounds(mText, 0, mText.length, mRect)
        mTextWidth = mRect.width()
    }

    fun setMarqueeText(resId: Int) {
        setText(resId)
        mText = text.toString()
        val textPaint = paint
        textPaint.getTextBounds(mText, 0, mText.length, mRect)
        mTextWidth = mRect.width()
    }


    private inner class MyTimerTask : TimerTask() {
        override fun run() {
            if (mTextWidth < width) { //文本长度小于View长度， 不进行滚动
                mOffsetX = 0
                scrollTo(0, 0) //滚动到初始为止，如果不调用此方法，可能会导致文本被遮挡
                postInvalidate()
                return
            }
            if (mOffsetX < -(mTextWidth / 2) - paddingEnd) {
                mOffsetX = width
            }
            mOffsetX -= mSpeed
            postInvalidate()
        }
    }

    override fun onDraw(canvas: Canvas) {
        mText = text.toString()
        val textPaint = paint
        textPaint.color = currentTextColor
        //获取文本区域大小，保存在mRect中。
        textPaint.getTextBounds(mText, 0, mText.length, mRect)
        mRect.right = width //将绘画区域的右边设置为textview的宽度值
        var mTextCenterVerticalToBaseLine =
            (-textPaint.ascent() + textPaint.descent()) / 2 - textPaint.descent()
        canvas.drawText(
            mText,
            mOffsetX.toFloat(),
            height / 2 + mTextCenterVerticalToBaseLine,
            textPaint
        )
    }

    /**
     * 视图移除时销毁任务和定时器
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.e(TAG, "killTimer")
        if (mTimerTask != null) {
            mTimerTask!!.cancel()
            mTimerTask = null
        }
        if (mTimer != null) {
            mTimer!!.cancel()
            mTimer = null
        }
    }

    fun setSpeed(speed: Int) {
        this.mSpeed = speed
    }

    companion object {
        private const val TAG = "AlwaysMarqueeTextView"
        private const val PFS = 24
    }
}