package com.metathought.food_order.casheir.ui.ordered

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Resources
import android.graphics.PointF
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSON
import com.google.gson.Gson
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.AttachPopupView
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.AcceptOrderedStatusEnum
import com.metathought.food_order.casheir.constant.FeatureMenuEnum
import com.metathought.food_order.casheir.constant.GoodTypeEnum
import com.metathought.food_order.casheir.constant.ONE_DAY_WITH_SECOND
import com.metathought.food_order.casheir.constant.OrderedStatusEnum
import com.metathought.food_order.casheir.constant.OrderedStatusMergeEnum
import com.metathought.food_order.casheir.constant.PayTypeEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.constant.SceneEnum
import com.metathought.food_order.casheir.constant.SourcePlatformEnum
import com.metathought.food_order.casheir.constant.WsCommand
import com.metathought.food_order.casheir.data.model.base.request_model.GoodsBo
import com.metathought.food_order.casheir.data.model.base.request_model.ReserveTableRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.SingleDiscountRequest
import com.metathought.food_order.casheir.data.model.base.request_model.ordered.UpdateCustomerInfoRequest
import com.metathought.food_order.casheir.data.model.base.response_model.coupon.CouponModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.PaymentResponse
import com.metathought.food_order.casheir.data.model.base.response_model.order.WsModifyWeight
import com.metathought.food_order.casheir.data.model.base.response_model.order.WsRefundModel
import com.metathought.food_order.casheir.data.model.base.response_model.order.WsReserveOrderCancel
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.ConsumerResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedRecord
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListItem
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTableListResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedTotalResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsAcceptOrderChangeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsMergeOrderResponse
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.WsOrderPreSettlementChangeResponse
import com.metathought.food_order.casheir.data.model.base.response_model.takeout.TakeOutPlatformModel
import com.metathought.food_order.casheir.database.dao.ShoppingHelper
import com.metathought.food_order.casheir.databinding.DialogFilterTableListBinding
import com.metathought.food_order.casheir.databinding.DialogSearchNicknameViewBinding
import com.metathought.food_order.casheir.databinding.FragmentOrderedBinding
import com.metathought.food_order.casheir.databinding.PopupOrderFilterBinding
import com.metathought.food_order.casheir.event.SimpleEvent
import com.metathought.food_order.casheir.event.SimpleEventType
import com.metathought.food_order.casheir.extension.convertToTimestamp2
import com.metathought.food_order.casheir.extension.decimalFormatZeroDigit
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getAcceptText
import com.metathought.food_order.casheir.extension.getAcceptTypeBackGroundColor
import com.metathought.food_order.casheir.extension.getAcceptTypeColor
import com.metathought.food_order.casheir.extension.getPayText
import com.metathought.food_order.casheir.extension.getPayTypeColor
import com.metathought.food_order.casheir.extension.getScrollPosition
import com.metathought.food_order.casheir.extension.getSourcePlatform
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero1
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setEnable
import com.metathought.food_order.casheir.extension.setVisibleGone
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.helper.FoundationHelper
import com.metathought.food_order.casheir.helper.OrderHelper
import com.metathought.food_order.casheir.helper.PopupWindowHelper
import com.metathought.food_order.casheir.helper.PrinterDeviceHelper
import com.metathought.food_order.casheir.helper.UnReadAndUnPrintHelper
import com.metathought.food_order.casheir.network.ApiResponse
import com.metathought.food_order.casheir.network.GOODS_HAS_BEEN_MERGE_OR_SPLIT
import com.metathought.food_order.casheir.network.ORDER_INFO_CHANGE
import com.metathought.food_order.casheir.network.websocket.socket_model.SocketModel
import com.metathought.food_order.casheir.ui.adapter.FilterTableAdapter
import com.metathought.food_order.casheir.ui.adapter.FloorAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedAdapter
import com.metathought.food_order.casheir.ui.adapter.OrderedAdapter.Companion.COUNT_DOWN
import com.metathought.food_order.casheir.ui.adapter.OrderedInfoAdapter
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.dialog.CancelCreditDialog
import com.metathought.food_order.casheir.ui.dialog.CancelOrderDialog
import com.metathought.food_order.casheir.ui.dialog.CancelReasonDialog
import com.metathought.food_order.casheir.ui.dialog.CommissionDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ConfirmDialog
import com.metathought.food_order.casheir.ui.dialog.CreditDialog
import com.metathought.food_order.casheir.ui.dialog.DiscountActivityDialog
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetail
import com.metathought.food_order.casheir.ui.dialog.OrderAmountDetailDialog
import com.metathought.food_order.casheir.ui.dialog.OrderFilterNicknameDialog
import com.metathought.food_order.casheir.ui.ordered.note.EditRemarkDialog
import com.metathought.food_order.casheir.ui.dialog.PackPriceDetailDialog
import com.metathought.food_order.casheir.ui.dialog.ServiceFeeDetailDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.MixedPayDialog
import com.metathought.food_order.casheir.ui.dialog.pay_center.PayDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.EditSingleDiscountDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountDetailDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountDialog
import com.metathought.food_order.casheir.ui.dialog.single_discount.SingleDiscountGoods
import com.metathought.food_order.casheir.ui.dialog.take_out.ModifyTakeOutIdDialog
import com.metathought.food_order.casheir.ui.dialog.tmp_good.TmpGoodDialog
import com.metathought.food_order.casheir.ui.member.balace.topup.TopUpSuccessDialog
import com.metathought.food_order.casheir.ui.member.balace.topup.TopupBalanceDialog
import com.metathought.food_order.casheir.ui.order.MenuOrderFragment
import com.metathought.food_order.casheir.ui.order.payment.PaymentSuccessDialog
import com.metathought.food_order.casheir.ui.order.table_available.AvailableTableListDialog
import com.metathought.food_order.casheir.ui.ordered.anti_settlement.AntiSettlementDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.CouponListDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.GiftProductsListDialog
import com.metathought.food_order.casheir.ui.ordered.coupon.IdentifyCouponDialog
import com.metathought.food_order.casheir.ui.ordered.discount.NewModifyDiscountDialog
import com.metathought.food_order.casheir.ui.ordered.merge.MergeOrderListDialog
import com.metathought.food_order.casheir.ui.ordered.printer.PrinterConfirmDialog
import com.metathought.food_order.casheir.ui.ordered.printer.PrinterConfirmViewModel
import com.metathought.food_order.casheir.ui.ordered.receive.CancelReceiveOrderGoodsListDialog
import com.metathought.food_order.casheir.ui.ordered.receive.WaitReceiveOrderGoodsListDialog
import com.metathought.food_order.casheir.ui.ordered.refunds.NewCancelDishDialog
import com.metathought.food_order.casheir.ui.ordered.refunds.RequestRefundsDialog
import com.metathought.food_order.casheir.ui.ordered.tablelist.SwitchTableByOrderDialog
import com.metathought.food_order.casheir.ui.ordered.time_price.EditTimePriceDialog
import com.metathought.food_order.casheir.ui.ordered.weight.EditWeightDialog
import com.metathought.food_order.casheir.ui.ordered.weight.MealSetWeightGoodsDialog
import com.metathought.food_order.casheir.ui.ordered.weight.WeightData
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.table.reserve.ReserveInputInfoDialog
import com.metathought.food_order.casheir.ui.widget.CustomBubbleAttachPopup
import com.metathought.food_order.casheir.utils.FileUtil
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.metathought.food_order.casheir.utils.TimeUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.tinder.scarlet.Message
import com.tinder.scarlet.WebSocket
import com.vj.navigationcomponent.helper.NetworkHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import java.math.BigDecimal
import java.util.Date
import java.util.Timer
import java.util.TimerTask


class OrderedFragment : BaseFragment() {

    companion object {
        fun newInstance() = OrderedFragment()


        const val ORDERED_FRAGMENT_BUNDLE_DATA = "ORDERED_FRAGMENT_BUNDLE_DATA"

        //确认订单
        const val CONFIRMED_ORDER = "Confirmed_Order"

        //定位到的ORDER_ID
        const val LOCAL_ORDER_ID = "LOCAL_ORDER_ID"

        //加购的菜
        const val ORDER_MORE = "ORDER_MORE"

        //餐桌跳转过来
        const val TABLE_UUID = "TABLE_UUID"
        const val PAY_STATUS = "PAY_STATUS"

        //是否需要打印
        const val IS_NEED_PRINT = "IS_NEED_PRINT"

        //是否已支付
        const val IS_FINISH = "IS_FINISH"

        //取消加购
//        const val CANCEL_ORDER_MORE_BACK = "CANCEL_ORDER_MORE_BACK"
    }

    private var popupViewTable: DialogFilterTableListBinding? = null
    private var popupViewSearchNickname: DialogSearchNicknameViewBinding? = null

    private val viewModel: OrderedViewModel by viewModels()
    private val printerViewModel: PrinterConfirmViewModel by viewModels()
    private var _binding: FragmentOrderedBinding? = null
    private var orderedAdapter: OrderedAdapter? = null


    private var orderedScreen: SecondaryScreenUI? = null
    private val binding get() = _binding
    private var orderedInfoAdapter: OrderedInfoAdapter? = null

    private var selectedValue = OrderedStatusEnum.All.id
    private var selectedTableItem = ArrayList<OrderedTableListItem>()
    private var totalPrice: Long = 0
    private var totalVipPrice: BigDecimal = BigDecimal.ZERO

    private var filterTableAdapter: FilterTableAdapter? = null
    private var floorAdapter: FloorAdapter? = null

    private var currentCheckId: Int? = null
    private var isFilterUnPrint = false
    private var isFilterUnRead = false
    private var selecteAccount: ConsumerResponse? = null

    private val searchRunnable = Runnable {
        try {
            binding?.apply {
                viewModel.getOrderedList(
                    true,
                    selectedValue,
                    keyword = edtSearch.getSearchContent(),
                    listTable = selectedTableItem,
                    isFilterUnRead = isFilterUnRead,
                    isFilterUnPrint = isFilterUnPrint,
                    consumerId = selecteAccount?.id
                )
            }
        } catch (e: Exception) {

        }
    }

    private fun postSearch(duration: Int = 500) {
        binding?.apply {
            edtSearch.removeCallbacks(searchRunnable)
            if (duration <= 0) {
                searchRunnable.run()
            } else {
                edtSearch.postDelayed(searchRunnable, duration.toLong())
            }
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentOrderedBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initEventBus()
        initSecondary()
        initView()
        initListener()
        initObserver()

    }


    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        stopCountDown()

        try {
            PayDialog.dismissDialog(parentFragmentManager)
        } catch (e: Exception) {

        }

        OrderAmountDetailDialog.dismissDialog(parentFragmentManager)
        WaitReceiveOrderGoodsListDialog.dismissDialog(parentFragmentManager)
        MergeOrderListDialog.dismissDialog(parentFragmentManager)

        super.onDestroy()
    }


    private fun initView() {

        updateUnRead()

        context?.let { context ->
            binding?.apply {
                orderedAdapter = OrderedAdapter(requireContext(), arrayListOf()) { record ->
                    currentCheckId = null
                    orderedInfoAdapter?.clearSelect()
                    viewModel.getOrderedInfo(record, isRead = true)
                }
                orderListRecyclerView.adapter = orderedAdapter

                orderedInfoAdapter =
                    OrderedInfoAdapter(arrayListOf(), onItemClickListener = { index, item ->
                        isShowBtnWeigh()
                        isShowBtnSetTimePrice()
                        showCancelDishBtn()
                        showDiscountBtn(viewModel.currentOrderedInfo)
                        isRemarkBtnEnable(viewModel.currentOrderedInfo)
                    }, onWarnClick = { view, item ->
                        SingleDiscountDetailDialog.showDialog(
                            parentFragmentManager,
                            orderGood = item,
                            orderedInfoResponse = viewModel.currentOrderedInfo
                        )
                    })

                orderedInfoRecyclerView.adapter = orderedInfoAdapter

                //跳转过的确定订单
                var orderNo: String? = null
                if (arguments?.containsKey(CONFIRMED_ORDER) == true) {
                    orderNo = arguments?.getString(CONFIRMED_ORDER, null)
                }


                //加购的菜
                var currentOrderMore: ArrayList<GoodsBo>? = null
                if (arguments?.containsKey(ORDER_MORE) == true) {
                    currentOrderMore = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        arguments?.getParcelableArrayList(ORDER_MORE, GoodsBo::class.java)
                    } else {
                        arguments?.getParcelableArrayList<GoodsBo>(ORDER_MORE)
                    }

                    if (currentOrderMore == null) {
                        currentOrderMore = arrayListOf()
                    }
                }

                var localOrderId: String? = null
                if (arguments?.containsKey(LOCAL_ORDER_ID) == true) {
                    localOrderId = arguments?.getString(LOCAL_ORDER_ID, null)
                }

                var isNeedPrint = false
                if (arguments?.containsKey(IS_NEED_PRINT) == true) {
                    isNeedPrint = arguments?.getBoolean(IS_NEED_PRINT) ?: false
                }

                var finishPay = false
                if (arguments?.containsKey(IS_FINISH) == true) {
                    finishPay = arguments?.getBoolean(IS_FINISH) ?: false
                }
                if (arguments?.containsKey(TABLE_UUID) != true) {
                    viewModel.getOrderedList(
                        confirmOrderNo = orderNo,
                        currentOrderMore = currentOrderMore,
                        localOrderNo = localOrderId,
                        isNeedPrint = isNeedPrint,
                        finishPay = finishPay,
                        consumerId = selecteAccount?.id
                    )
                }
            }
        }
    }

    private fun initSecondary() {
        context?.let {
            orderedScreen = MyApplication.myAppInstance.orderedScreen
            getOrderedScreen()?.showOrderedInfo()
        }
    }

    private fun getOrderedScreen(): SecondaryScreenUI? {
        return orderedScreen
    }

    private var orderFilterNicknameDialog: AttachPopupView? = null
    private fun initListener() {
        binding?.apply {
            btnUnRead.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (!isFilterUnRead && UnReadAndUnPrintHelper.getUnReadNum() == 0) {
                        //未选中未读数 且没未打印数量的时候不让选
                        return@isFastDoubleClick
                    }

                    isFilterUnPrint = false
                    isFilterUnRead = !isFilterUnRead
                    if (isFilterUnRead) {
                        orderListRecyclerView.scrollToPosition(0)
                    }
                    updateUnReadAndUnPrintBtn()
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem,
                        isFilterUnRead = isFilterUnRead,
                        isFilterUnPrint = isFilterUnPrint,
                        consumerId = selecteAccount?.id
                    )
                }
            }

            btnUnPrint.setOnClickListener {
                SingleClickUtils.isFastDoubleClick {
                    if (!isFilterUnPrint && UnReadAndUnPrintHelper.getUnPrintNum() == 0) {
                        //未选中未打印 且没未打印数量的时候不让选
                        return@isFastDoubleClick
                    }
                    isFilterUnRead = false
                    isFilterUnPrint = !isFilterUnPrint
                    if (isFilterUnPrint) {
                        orderListRecyclerView.scrollToPosition(0)
                    }
                    updateUnReadAndUnPrintBtn()
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem,
                        isFilterUnRead = isFilterUnRead,
                        isFilterUnPrint = isFilterUnPrint,
                        consumerId = selecteAccount?.id
                    )
                }
            }

            layoutOrderInfo.apply {
                refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                    override fun onRefresh(refreshLayout: RefreshLayout) {
                        viewModel.getOrderedList(
                            true,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem,
                            isFilterUnRead = isFilterUnRead,
                            isFilterUnPrint = isFilterUnPrint,
                            consumerId = selecteAccount?.id
                        )
                    }

                    override fun onLoadMore(refreshLayout: RefreshLayout) {
                        viewModel.getOrderedList(
                            false,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem,
                            isFilterUnRead = isFilterUnRead,
                            isFilterUnPrint = isFilterUnPrint,
                            consumerId = selecteAccount?.id
                        )
                    }

                })
                dropdownFilter.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        arrowFilter.animate().rotation(180f).duration = 200
                        dropdownFilter.setBackgroundResource(R.drawable.background_spinner_top)
                        showPopupFilterStatus(dropdownFilter)
                    }
                }
                dropdownTable.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        arrowTable.animate().rotation(180f).duration = 200
                        dropdownTable.setBackgroundResource(R.drawable.background_spinner_top)
                        showPopupTableView(dropdownTable)
                    }
                }
                initPopuptableView()
                dropdownNickname.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {

                        if (orderFilterNicknameDialog != null) {
                            return@isFastDoubleClick
                        }
                        arrowNickname.animate().rotation(180f).duration = 200
                        dropdownNickname.setBackgroundResource(R.drawable.background_spinner_top)

                        orderFilterNicknameDialog = OrderFilterNicknameDialog(
                            requireContext(),
                            it,
                            viewModel,
                            <EMAIL>,
                            confirmListener = { selecteAccount ->
                                <EMAIL> = selecteAccount
                                if (selecteAccount != null) {
                                    binding?.tvFilterNickname?.text = selecteAccount.nickName ?: ""
                                } else {
                                    binding?.tvFilterNickname?.text =
                                        getString(R.string.customer_nickname)
                                }
                                //重新获取订单列表
                                binding?.apply {
                                    viewModel.getOrderedList(
                                        true,
                                        selectedValue,
                                        keyword = edtSearch.getSearchContent(),
                                        listTable = selectedTableItem,
                                        isFilterUnRead = isFilterUnRead,
                                        isFilterUnPrint = isFilterUnPrint,
                                        consumerId = selecteAccount?.id
                                    )
                                }
                            }) {
                            orderFilterNicknameDialog = null
                            binding?.arrowNickname?.animate()?.rotation(0f)?.duration = 200
                            dropdownNickname.setBackgroundResource(R.drawable.background_language_spiner)
                        }.showDialog()
                    }
                }

                orderedInfoRecyclerView.addOnScrollListener(object :
                    RecyclerView.OnScrollListener() {
                    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                        super.onScrolled(recyclerView, dx, dy)
                        recyclerView.layoutManager?.getScrollPosition(dy)?.let {
                            if (it != -1) {
                                getOrderedScreen()?.updateOrderedInfoRecyclerView(it)
                            }
                        }
                    }
                })

                edtSearch.setTextChangedListenerCallBack {
                    resetFilter()
                    postSearch()
                }

                btnOrderMore.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        viewModel.currentOrderedInfo?.let {
                            it.diningStyle?.let { diningStyle ->
                                val shopping = ShoppingHelper.get(diningStyle)
                                if (shopping != null && shopping.getGoodsVoList().isNotEmpty()) {
                                    ConfirmDialog.showDialog(
                                        parentFragmentManager,
                                        positiveButtonTitle = getString(R.string.go_to_menu),
                                        content = getString(R.string.already_goods_shopping_cart_tip),
                                        negativeButtonTitle = getString(R.string.cancel)
                                    ) {
//                                orderMore()
                                        val currentFragment =
                                            parentFragmentManager.fragments.firstOrNull()
                                        val bundle = Bundle()
                                        bundle.putInt(
                                            ORDERED_FRAGMENT_BUNDLE_DATA,
                                            viewModel.currentOrderedInfo?.diningStyle ?: 0
                                        )
                                        if (currentFragment is MainDashboardFragment) {
                                            currentFragment.replaceFragmentFromOtherFragment(
                                                FeatureMenuEnum.ORDER.id,
                                                MenuOrderFragment(),
                                                bundle
                                            )
                                        }
                                    }
                                } else {
                                    orderMore()
                                }
                            }
                        }
                    }
                }

                //退菜
                btnCancelDish.setOnClickListener {
                    if (viewModel.uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        val good = orderedInfoAdapter?.getSelectItem()
                        if (good != null) {
                            /**
                             * 商品只有一个的时候 且不显示如果操作 直接调用接口不用弹窗
                             */
                            if (good.getCanRefundNum() == 1 && viewModel.currentOrderedInfo?.showAutoInStockBtn != true) {
                                good.refundsNum = 1
                                viewModel.changeGood(
                                    orderNo = viewModel.currentOrderedInfo?.orderNo!!,
                                    orderedInfoResponse = viewModel.currentOrderedInfo!!,
                                    autoInStock = false
                                )
                            } else {
                                viewModel.currentOrderedInfo?.let { it1 ->
                                    NewCancelDishDialog.showDialog(
                                        parentFragmentManager,
                                        it1,
                                        good,
                                        onConfirmClickListener = { order, autoInStock ->
                                            viewModel.changeGood(
                                                orderNo = order?.orderNo!!,
                                                orderedInfoResponse = it1,
                                                autoInStock = autoInStock
                                            )
                                        })
                                }
                            }
                        }
                    }

                }

                //cancel order
                btnCancelOrder.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }

                        if (viewModel.uiInfoState.value?.orderedInfoResponse is ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }

                        CancelOrderDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo?.orderNo,
                            isShowAutoInStore = viewModel.currentOrderedInfo?.showAutoInStockBtn
                        ) { reason, autoInStock ->
                            if (viewModel.currentOrderedInfo != null) {
//                            Timber.e("reason  ===${reason.trim()}===")
                                viewModel.cancelOrder(
                                    viewModel.currentOrderedInfo!!.id!!,
                                    reason.trim(),
                                    autoInStock = autoInStock
                                )
                            }
                        }
                    }
                }

                btnPay.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.uiRequestState.value?.paymentResponse == ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }
                        if (viewModel.currentOrderedInfo?.isHasNeedProcess() == true) {
                            showToast(getString(R.string.order_has_no_price_good))
                            return@isFastDoubleClick
                        }
                        PayDialog.showDialog(
                            fragmentManager = parentFragmentManager,
                            menuOrderScreen = orderedScreen,
                            currentScene = SceneEnum.ORDER.id,
                            orderInfo = viewModel.currentOrderedInfo,
                            conversionRatio = viewModel.currentOrderedInfo?.conversionRatio,
                            totalPrice = viewModel.currentOrderedInfo?.getRealPayPrice(),
                            totalVipPrice = if (viewModel.currentOrderedInfo?.isShowVipPrice() == true) viewModel.currentOrderedInfo?.vipPrice?.getTotalToLong() else null,
//                            countryCode = viewModel.currentOrderedInfo?.customerInfoVo?.areaCode,
//                            phone = viewModel.currentOrderedInfo?.customerInfoVo?.mobile,
                            countryCode = viewModel.currentOrderedInfo?.getConsumePhoneNumber()?.first,
                            phone = viewModel.currentOrderedInfo?.getConsumePhoneNumber()?.second,
                            paymentResponse = {
                                when (it) {
                                    is ApiResponse.Loading -> {
                                        showProgress()
                                        btnPay.setEnable(false)
                                    }

                                    is ApiResponse.Success -> {
                                        paymentCompleted(it.data)
                                    }

                                    is ApiResponse.Error -> {
                                        pbConfirm.isVisible = false
                                        btnPay.setEnable(true)
                                        dismissProgress()
                                        showToast(it.message ?: "")
                                        if (it.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                                            viewModel.getOrderedList(
                                                true,
                                                selectedValue,
                                                keyword = edtSearch.getSearchContent(),
                                                listTable = selectedTableItem,
                                                isFilterUnRead = isFilterUnRead,
                                                isFilterUnPrint = isFilterUnPrint,
                                                consumerId = selecteAccount?.id
                                            )
                                        } else {
                                            if (viewModel.currentOrderedInfo?.orderNo != null) {
                                                viewModel.getOrderedInfo(viewModel.currentOrderedInfo?.orderNo!!)
                                            }
                                        }
                                    }

                                    else -> {

                                    }
                                }
                            },
                            onlineSuccessListener = {
                                Timber.e(
                                    "!PrinterDeviceHelper.isOrderPrinter(it.orderNo)  ${
                                        !PrinterDeviceHelper.isOrderPrinter(
                                            it.orderNo
                                        )
                                    }"
                                )
                                if (!PrinterDeviceHelper.isOrderPrinter(it.orderNo)) {
                                    PrinterDeviceHelper.setOrderPrinter(it.orderNo)
                                    viewModel.updateOrderMoreScreen(it.orderNo)
                                    context?.let { context ->
                                        Timber.e("这里调用几次")
                                        viewModel.requestOrderedInfo(
                                            it.orderNo,
                                            finishPay = true,
                                            context = context,
                                            isNeedPrint = true
                                        )
                                    }
                                }
                                PaymentSuccessDialog.showDialog(
                                    parentFragmentManager, orderedInfo = it, orderedScreen
                                ) {
                                    getOrderedScreen()?.showOrderedInfo()
                                    getOrderedScreen()?.setOrderInfo(
                                        viewModel.currentOrderedInfo,
                                        viewModel.getOrderUsableCouponList()
                                    )
                                }
                            },
                            onCloseListener = {
                                getOrderedScreen()?.showOrderedInfo()
                                getOrderedScreen()?.setOrderInfo(
                                    viewModel.currentOrderedInfo,
                                    viewModel.getOrderUsableCouponList()
                                )
                            },
                            onTopUpListener = {
                                ConfirmDialog.showDialog(
                                    parentFragmentManager,
                                    content = getString(R.string.insufficient_balance),
                                    positiveButtonTitle = getString(R.string.top_up),
                                    negativeButtonTitle = getString(R.string.change_payment),
                                ) {
                                    MixedPayDialog.dismissDialog(parentFragmentManager)
                                    PayDialog.dismissDialog(parentFragmentManager)
                                    TopupBalanceDialog.showDialog(
                                        parentFragmentManager,
                                        menuOrderScreen = orderedScreen,
                                        content = it.toJson()
                                    ) { response ->
                                        if (response) {
                                            TopUpSuccessDialog.showDialog(
                                                parentFragmentManager
                                            ) {
                                                getOrderedScreen()?.showOrder()
                                            }
                                        } else {
                                            getOrderedScreen()?.showOrder()
                                        }
                                    }
                                }
                            })
                    }
                }

                tvClearFilter.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        resetFilter()
                        binding?.apply {
                            activity?.hideKeyboard(edtSearch.getEditText()!!)
                        }
                        viewModel.getOrderedList(true)
                    }
                }

                btnOrderRefund.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        viewModel.currentOrderedInfo?.let { it1 ->
                            RequestRefundsDialog.showDialog(
                                parentFragmentManager,
                                it1,
                                orderedScreen = getOrderedScreen(),
                                fullRefundClickListener = { order, type, totalPrice, autoInStock ->
                                    ConfirmDialog.showDialog(
                                        parentFragmentManager,
                                        content = getString(
                                            R.string.refund_amount_confirm, totalPrice
                                        ),
                                        positiveButtonTitle = getString(R.string.refund_btn),
                                        negativeButtonTitle = getString(R.string.cancel),
                                    ) {
                                        viewModel.refund(
                                            orderNo = order?.orderNo,
                                            type,
                                            autoInStock = autoInStock
                                        )
                                    }
                                },

                                partialRefundClickListener = { order, type, totalPrice, autoInStock ->
                                    order?.goods?.let { goods ->
                                        ConfirmDialog.showDialog(
                                            parentFragmentManager,
                                            content = getString(
                                                R.string.refund_amount_confirm, totalPrice
                                            ),
                                            positiveButtonTitle = getString(R.string.refund_btn),
                                            negativeButtonTitle = getString(R.string.cancel),
                                        ) {
                                            viewModel.refund(
                                                orderNo = order.orderNo,
                                                type,
                                                fundGoods = goods,
                                                autoInStock = autoInStock
                                            )
                                        }
                                    }

                                })
                        }
                    }
                }

                llPackPrice.setOnClickListener {
                    if (!btnPackPriceCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        val list = viewModel.currentOrderedInfo?.goods?.map {
                            it.orderedGoodsConvertToGoods()
                        }

                        if (list.isNullOrEmpty()) {
                            return@isFastDoubleClick
                        }

                        PackPriceDetailDialog.showDialog(
                            activity?.supportFragmentManager ?: parentFragmentManager,
                            list,
                            viewModel.currentOrderedInfo?.conversionRatio,
                            TakeOutPlatformModel(
                                id = viewModel.currentOrderedInfo?.id,
                                name = viewModel.currentOrderedInfo?.deliveryPlatformName,
                                type = viewModel.currentOrderedInfo?.deliveryPlatformCurrencyType
                            )
                        )
                    }
                }

                llServiceFee.setOnClickListener {
                    if (!btnServiceFeeCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        val list = viewModel.currentOrderedInfo?.goods?.map {
                            it.setServiceChargePercentage(viewModel.currentOrderedInfo!!.getServicePercentage())
                            it.orderedGoodsConvertToGoods()
                        }

                        if (list.isNullOrEmpty()) {
                            return@isFastDoubleClick
                        }

                        ServiceFeeDetailDialog.showDialog(
                            activity?.supportFragmentManager ?: parentFragmentManager,
                            list,
                            viewModel.currentOrderedInfo
                        )
                    }
                }

                //待确认点击确认
                btnSubmit.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        context?.let {
                            if (viewModel.currentOrderedInfo?.isHasNeedProcess() == true) {
                                showToast(getString(R.string.order_has_no_price_good))
                                return@isFastDoubleClick
                            }
                            viewModel.submitOrder(it)
                        }
                    }

                }

                btnChangeTable.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        AvailableTableListDialog.showDialog(
                            activity?.supportFragmentManager ?: parentFragmentManager,
                            currentOrderedInfo = viewModel.currentOrderedInfo,
                            changeOrderLister = { allInUnpaid, newTable, orderNo ->
                                viewModel.orderToChangeTable(allInUnpaid, newTable, orderNo)
                            })
                    }
                }

                //折扣价
                btnWholeDiscount.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        NewModifyDiscountDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo,
                            reduceDiscountDetailModel = null,
                            cartGoods = null,
//                        couponCode = null,
                            takeOutPlatformModel = null,
                            { reduceDiscountDetailRequest ->
                                viewModel.getOrderedInfo(viewModel.currentOrderedInfo?.orderNo!!)
                            },
                            { errorCode ->
                                if (errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                                    viewModel.getOrderedList(
                                        true,
                                        selectedValue,
                                        keyword = edtSearch.getSearchContent(),
                                        listTable = selectedTableItem,
                                        isFilterUnRead = isFilterUnRead,
                                        isFilterUnPrint = isFilterUnPrint,
                                        consumerId = selecteAccount?.id
                                    )
                                } else {
                                    viewModel.getOrderedInfo(viewModel.currentOrderedInfo?.orderNo!!)
                                }
                            })
                    }
                }

                btnSingleDiscount.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        val selectItem = orderedInfoAdapter?.getSelectItem()
                        if (selectItem != null) {
                            val singleDiscountGood = SingleDiscountGoods(
                                name = selectItem.name,
                                num = selectItem.num,
                                totalPrice = selectItem.totalPrice(),
                                vipPrice = selectItem.totalVipPrice(),
                                isShowVipPrice = selectItem.isShowVipPrice(),
                                goodsId = selectItem.id,
                                goodsHashKey = selectItem.hashKey,
                                type = selectItem.singleItemDiscount?.type,    //优惠类型
                                reduceRatio = selectItem.singleItemDiscount?.reduceRatio,    //减免（百分比）
                                saleReduce = selectItem.singleItemDiscount?.saleReduce?.toDouble(),    //销售减免
                                vipReduce = selectItem.singleItemDiscount?.vipReduce?.toDouble(),    //会员减免
                                remark = selectItem.singleItemDiscount?.remark,
                                singlePrice = selectItem.getCurrentFinalPrice(),
                                singleVipPrice = selectItem.finalVipPrice,
                                adjustVipPrice = selectItem.singleItemDiscount?.adjustVipPrice,
                                adjustSalePrice = selectItem.singleItemDiscount?.adjustSalePrice,
                                discountType = selectItem.singleItemDiscount?.discountType,
                                discountReduceActivityId = selectItem.singleItemDiscount?.discountReduceActivityId,
                                discountReduceInfo = selectItem.singleItemDiscount?.discountReduceActivity
                            )

                            EditSingleDiscountDialog.showDialog(
                                parentFragmentManager,
                                isOnlyModifyPrice = false,
                                singleDiscountGoods = singleDiscountGood,
                                orderNo = viewModel.currentOrderedInfo?.orderNo,
                                positiveButtonListener = { data ->
//                                    val remark: String? = data?.remark
                                    val orderNo = viewModel.currentOrderedInfo?.orderNo
                                    if (orderNo == null) {
                                        return@showDialog
                                    }
                                    viewModel.setSingleReduceDiscount(
                                        orderNo, "", listOf(
                                            SingleDiscountRequest(
                                                type = data?.type,
                                                goodsId = data?.goodsId,
                                                goodsHashKey = data?.goodsHashKey,
                                                reduceRatio = data?.reduceRatio,
                                                saleReduce = data?.saleReduce,
                                                vipReduce = data?.vipReduce,
                                                remark = data?.remark,
                                                adjustSalePrice = data?.adjustSalePrice,
                                                adjustVipPrice = data?.adjustVipPrice,
                                                goodsNum = data?.num,
                                                discountType = data?.discountType,
                                                discountReduceActivityId = data?.discountReduceActivityId
                                            )
                                        )
                                    )
                                    SingleDiscountDialog.dismissDialog(parentFragmentManager)
                                })
                        }
                    }
                }

                btnCoupon.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.currentOrderedInfo?.orderNo == null) {
                            return@isFastDoubleClick
                        }
                        //支付后不能选券
                        if (viewModel.currentOrderedInfo?.isAfterPayStatus() == true) {
                            return@isFastDoubleClick
                        }
                        var isShowVip = false
                        binding?.apply {
                            isShowVip = tvVipPrice.isVisible
                        }

                        if (viewModel.getOrderUsableCouponList()
                                .isEmpty() && viewModel.currentOrderedInfo?.getCurrentCoupon() == null
                        ) {
                            //如果没有可用优惠券 且当前订单没有优惠券 则显示识别优惠券  点击跳转识别优惠券
                            IdentifyCouponDialog.showDialog(
                                activity?.supportFragmentManager ?: parentFragmentManager,
                                orderNo = viewModel.currentOrderedInfo?.orderNo!!
                            ) {
                                viewModel.updateCouponInfo(it)
                            }

                            return@isFastDoubleClick
                        }

                        var list: List<CouponModel>? = null
                        if (viewModel.getOrderUsableCouponList().isEmpty()) {
                            //如果可用列表是空的
                            if (viewModel.currentCouponInfo != null && viewModel.currentOrderedInfo?.getCurrentCoupon() != null && viewModel.currentCouponInfo?.id == viewModel.currentOrderedInfo?.getCurrentCoupon()?.id) {
                                list = listOf(viewModel.currentCouponInfo!!)
                            } else if (viewModel.currentCouponInfo == null && viewModel.currentOrderedInfo?.getCurrentCoupon() != null) {
                                //用来处理如果当前订单适用的优惠券不满意条件的时候 优惠券列表请求是不会返回该券，需要传进去
                                list = listOf(
                                    viewModel.currentOrderedInfo?.getCurrentCoupon()!!
                                        .toCouponModel()
                                )
                            }
                        }

                        CouponListDialog.showDialog(
                            activity?.supportFragmentManager ?: parentFragmentManager,
                            orderNo = viewModel.currentOrderedInfo?.orderNo!!,
                            currentCouponId = viewModel.currentOrderedInfo?.couponId,
                            localCouponList = list,
                            isShowVip = isShowVip,
                            isHasUnWeight = viewModel.currentOrderedInfo?.isHasNeedProcess()
                        ) {
                            viewModel.updateCouponInfo(it)
                        }

                    }
                }

                tvViewCouponGiftGood.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        GiftProductsListDialog.showDialog(
                            fragmentManager = parentFragmentManager,
                            viewModel.currentOrderedInfo?.getGiftGoodsList()
                        )
                    }
                }


                /**
                 * 备注
                 */
                btnRemark.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        EditRemarkDialog.showDialog(
                            parentFragmentManager,
                            orderInfo = viewModel.currentOrderedInfo,
                            orderedGood = orderedInfoAdapter?.getSelectItem(),
                            remark = if (orderedInfoAdapter?.getSelectItem() == null) viewModel.currentOrderedInfo?.getFinalNote() else orderedInfoAdapter?.getSelectItem()
                                ?.getFinalNote(),
                            updateOrderCallBackListener = {
                                tvOrderRemark.text = it?.note
                                viewModel.updateOrderInfo(it)
                            })
                    }
                }

                /**
                 * 返回按钮
                 */
                btnBackMenu.setOnClickListener {
                    //返回菜单
                    SingleClickUtils.isFastDoubleClick {
                        val currentFragment = parentFragmentManager.fragments.firstOrNull()
                        val bundle = Bundle()
                        if (currentFragment is MainDashboardFragment) {
                            currentFragment.replaceFragmentFromOtherFragment(
                                FeatureMenuEnum.ORDER.id, MenuOrderFragment(), bundle
                            )
                        }
                    }
                }

                btnPrint.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1500) {

                        PrinterConfirmDialog.showDialog(
                            parentFragmentManager,
                            orderID = viewModel.currentOrderedInfo?.id ?: "",
                            orderInfo = viewModel.currentOrderedInfo,
                        ) { casheirCheck, kitchenCheckType, isPreSettlement, isPrintLabel ->
                            context?.let { it1 ->
                                viewModel.printerAgain(
                                    it1,
                                    casheirCheck,
                                    kitchenCheckType,
                                    isPreSettlement,
                                    isPrintLabel,
                                    printerViewModel
                                )
                            }
                        }
                    }
                }

                btnExport.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1500) {
                        if (checkPermissions()) {
                            exportAgain()
                        } else {
                            requestPermissions()
                        }
                    }
                }

                btnNoAccept.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        CancelOrderDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo?.orderNo,
                            getString(R.string.sure_no_accept_this_order)
                        ) { reason, autoInStock ->

                            if (viewModel.currentOrderedInfo != null) {
                                viewModel.cancelAcceptOrder(
                                    reason.trim()
                                )
                            }
                        }
                    }
                }

                //接单
                btnAcceptOrder.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        viewModel.receivingOrder(requireContext())
                    }
                }

                //待接单查看全部
                tvViewAllReceiveInfo.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        val orderInfo = viewModel.currentOrderedInfo
                        WaitReceiveOrderGoodsListDialog.showDialog(
                            parentFragmentManager,
                            orderInfo,
                            {
                                CancelOrderDialog.showDialog(
                                    parentFragmentManager,
                                    orderInfo?.orderNo,
                                    getString(R.string.sure_no_accept_this_order)
                                ) { reason, autoInStock ->
                                    handlerAfterOrderCancel(orderInfo?.orderNo)
                                    if (orderInfo != null) {
                                        viewModel.cancelAcceptOrder(
                                            reason.trim()
                                        )
                                    }
                                }
                            },
                            {
                                viewModel.receivingOrder(requireContext())
                            })
                    }
                }

                //已取消接单 按钮
                llReceiveCancelLayout.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        CancelReceiveOrderGoodsListDialog.showDialog(
                            parentFragmentManager, viewModel.currentOrderedInfo
                        )
                    }
                }


                btnWeight.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        val item = orderedInfoAdapter?.getSelectItem()
                        if (item != null) {
                            if (item.isMealSet()) {
                                //套餐子商品选择弹窗
                                MealSetWeightGoodsDialog.showDialog(
                                    parentFragmentManager,
                                    item.orderMealSetGoodsDTOList,
                                ) { orderGoods ->
                                    viewModel.confirmPendingGoods(
                                        requireContext(),
                                        viewModel.currentOrderedInfo?.orderNo!!,
                                        item,
                                        orderGoods.weight?.toString(),
                                        type = 2,
                                        mealSetWeighingGoodsKey = orderGoods.hashKey
                                    )
                                }
                            } else {
                                EditWeightDialog(requireContext()).showDialog(
                                    WeightData(
                                        weight = item.weight,
                                        weightUnit = item.getWeightUnit(),
                                        singleDiscount = item.getCurrentPriceWithOutSingleDiscount(),
                                    )
                                ) { weight ->
                                    if (weight.toDoubleOrNull() != item?.weight || item.isSetSingleItemDiscount()) {
                                        viewModel.confirmPendingGoods(
                                            requireContext(),
                                            viewModel.currentOrderedInfo?.orderNo!!,
                                            item,
                                            weight,
                                            type = 2
                                        )
                                    }
                                }
                            }
                        }
                    }
                }

                btnTimePrice.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        val item = orderedInfoAdapter?.getSelectItem()
                        if (item != null) {
                            EditTimePriceDialog.showDialog(
                                parentFragmentManager,
                                orderInfo = viewModel.currentOrderedInfo,
                                orderedGood = item,
                                addToCart = false,
                                diningStyle = viewModel.currentOrderedInfo?.diningStyle!!,
                                updateOrderCallBackListener = { sellPrice, vipPrice ->
                                    viewModel.confirmPendingGoods(
                                        requireContext(),
                                        viewModel.currentOrderedInfo?.orderNo!!,
                                        item,
                                        sellPrice = sellPrice,
                                        vipPrice = vipPrice,
                                        type = 1
                                    )
                                })
                        }
                    }
                }

                btnAntiSettlement.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        AntiSettlementDialog(requireContext()).showDialog(viewModel.currentOrderedInfo) { orderInfo, reasonType, reason ->
                            viewModel.antiSettlement(
                                requireContext(), orderInfo, reasonType + 1, reason
                            )
                        }
                    }
                }

                llTotalPrice.setOnClickListener {
                    if (!btnTotalPriceDetail.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick(1000) {
                        val list = viewModel.currentOrderedInfo?.goods?.map {
                            it.setServiceChargePercentage(viewModel.currentOrderedInfo!!.getServicePercentage())
                            it.orderedGoodsConvertToGoods()
                        }

                        if (list.isNullOrEmpty()) {
                            return@isFastDoubleClick
                        }

                        OrderAmountDetailDialog.showDialog(
                            parentFragmentManager, orderAmountDetail = OrderAmountDetail(
                                goods = list, orderedInfo = viewModel.currentOrderedInfo
                            )
                        )
                    }
                }

                btnMerge.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick(1000) {
                        if (viewModel.uiTableState.value?.response is ApiResponse.Success) {
                            MergeOrderListDialog.showDialog(
                                parentFragmentManager,
                                viewModel.orderRecord,
                                mergeSuccessListener = {
                                    viewModel.getOrderedList(
                                        true,
                                        selectedValue,
                                        keyword = edtSearch.getSearchContent(),
                                        listTable = selectedTableItem,
                                        localOrderNo = it?.orderNo,
                                        isFilterUnRead = isFilterUnRead,
                                        isFilterUnPrint = isFilterUnPrint,
                                        consumerId = selecteAccount?.id
                                    )
                                },
                                mergeErrorListener = {
                                    viewModel.getOrderedList(
                                        true,
                                        selectedValue,
                                        keyword = edtSearch.getSearchContent(),
                                        listTable = selectedTableItem,
                                        localOrderNo = it?.orderNo,
                                        isFilterUnRead = isFilterUnRead,
                                        isFilterUnPrint = isFilterUnPrint,
                                        consumerId = selecteAccount?.id
                                    )
                                })
                        }
                    }
                }

                btnSplit.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        ConfirmDialog.showDialog(
                            parentFragmentManager,
                            content = getString(R.string.sure_to_splite_order),
                        ) {
                            //如果存在未完成的订单，则跳转到订单页，查询当前桌台下的订单
                            viewModel.splitOrder(viewModel.currentOrderedInfo?.orderNo)
                        }
                    }
                }

                llDiscountActivity.setOnClickListener {
                    if (!btnDiscountActivityPriceCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick(1000) {
                        DiscountActivityDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo?.couponActivityList ?: listOf(),
                            tvVipPrice.isVisible,
                            viewModel.currentOrderedInfo
                        )
                    }
                }

                /**
                 * 临时菜品
                 */
                btnTmpGood.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        TmpGoodDialog.showDialog(parentFragmentManager) {
                            viewModel.addTmpGoodToCart(requireContext(), it) {
                                TmpGoodDialog.dismissDialog(parentFragmentManager)
                            }
                        }
                    }
                }

                /**
                 * 编辑客户信息
                 */
                ivEditCustomer.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        ReserveInputInfoDialog.showDialog(
                            parentFragmentManager, orderInfo = viewModel.currentOrderedInfo
                        ) { reserveTableRequest ->
                            viewModel.updateCustomerInfo(
                                reserveTableRequest.name,
                                reserveTableRequest.areaCode,
                                reserveTableRequest.mobile,
                                reserveTableRequest.diningNumber,
                                reserveTableRequest.diningTime,
                                viewModel.currentOrderedInfo?.orderNo
                            )
//                            viewModel.reserverTable(reserveTableRequest)
                        }
                    }
                }

                llCommission.setOnClickListener {
                    if (!btnCommissionCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        val list = viewModel.currentOrderedInfo?.goods?.map {
                            it.orderedGoodsConvertToGoods()
                        }

                        if (list.isNullOrEmpty()) {
                            return@isFastDoubleClick
                        }

                        CommissionDetailDialog.showDialog(
                            parentFragmentManager, list, TakeOutPlatformModel(
                                id = viewModel.currentOrderedInfo?.id,
                                name = viewModel.currentOrderedInfo?.deliveryPlatformName,
                                type = viewModel.currentOrderedInfo?.deliveryPlatformCurrencyType
                            ), viewModel.currentOrderedInfo?.conversionRatio
                        )
                    }
                }

                ivEditTakeOutId.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        ModifyTakeOutIdDialog.showDialog(
                            parentFragmentManager, viewModel.currentOrderedInfo?.deliveryOrderNo
                        ) {
                            viewModel.editDeliveryOrderNo(it)
                        }
                    }
                }

                llDiscount.setOnClickListener {
                    if (!btnDiscountCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.currentOrderedInfo?.getWholeDiscountReason()
                                .isNullOrEmpty()
                        ) {
                            return@isFastDoubleClick
                        }
                        val location = IntArray(2)
                        btnDiscountCue.getLocationOnScreen(location)
                        val x = location[0] + btnDiscountCue.width / 2
                        val y = location[1]
                        XPopup.Builder(requireContext()).hasShadowBg(false).isTouchThrough(true)
                            .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                            .atPoint(PointF(x.toFloat(), y.toFloat())).isCenterHorizontal(true)
                            .hasShadowBg(false) // 去掉半透明背景
                            .asCustom(
                                CustomBubbleAttachPopup(
                                    requireContext(),
                                    viewModel.currentOrderedInfo?.getWholeDiscountReason()
                                )
                            ).show()
                    }
                }

                llDiscountAmount.setOnClickListener {
                    if (!btnDiscountAmountCue.isVisible) {
                        return@setOnClickListener
                    }
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.currentOrderedInfo?.getWholeDiscountReason()
                                .isNullOrEmpty()
                        ) {
                            return@isFastDoubleClick
                        }
                        val location = IntArray(2)
                        btnDiscountAmountCue.getLocationOnScreen(location)
                        val x = location[0] + btnDiscountAmountCue.width / 2
                        val y = location[1]
                        XPopup.Builder(requireContext()).hasShadowBg(false).isTouchThrough(true)
                            .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                            .atPoint(PointF(x.toFloat(), y.toFloat())).isCenterHorizontal(true)
                            .hasShadowBg(false) // 去掉半透明背景
                            .asCustom(
                                CustomBubbleAttachPopup(
                                    requireContext(),
                                    viewModel.currentOrderedInfo?.getWholeDiscountReason()
                                )
                            ).show()
                    }
                }
                btnCancelCredit.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.uiInfoRequestState.value?.baseBooleanResponse is ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }

                        if (viewModel.uiInfoState.value?.orderedInfoResponse is ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }

                        CancelCreditDialog.showDialog(
                            parentFragmentManager,
                            viewModel.currentOrderedInfo?.orderNo,
                            isShowAutoInStore = viewModel.currentOrderedInfo?.showAutoInStockBtn
                        ) { reason, autoInStock ->
                            if (viewModel.currentOrderedInfo != null) {
                                viewModel.cancelCredit(
                                    viewModel.currentOrderedInfo!!.id!!,
                                    reason.trim(),
                                    autoInStock = autoInStock
                                )
                            }
                        }
                    }
                }
                btnCredit.setOnClickListener {
                    SingleClickUtils.isFastDoubleClick {
                        if (viewModel.uiRequestState.value?.paymentResponse == ApiResponse.Loading) {
                            return@isFastDoubleClick
                        }
                        if (viewModel.currentOrderedInfo?.isHasNeedProcess() == true) {
                            showToast(getString(R.string.order_has_no_price_good))
                            return@isFastDoubleClick
                        }
                        //订单归属手机号
                        val orderInfo = viewModel.currentOrderedInfo ?: return@isFastDoubleClick
                        val finalTotalPrice = orderInfo.getRealPayPrice()
                        val (areaCode, mobile) = orderInfo.getConsumePhoneNumber() ?: Pair("", "")
                        CreditDialog.showDialog(
                            parentFragmentManager, finalTotalPrice, CreditDialog.AccountInfo(
                                accountId = null,
                                nickName = orderInfo.customerName,
                                areaCode = areaCode,
                                telephone = mobile,
                            )
                        ) { aInfo ->
                            viewModel.payAgain(
                                PayTypeEnum.CREDIT,
                                accountId = aInfo.accountId,
                                nickName = aInfo.nickName,
                                telephone = "${aInfo.areaCode}${aInfo.telephone}",
                                reason = aInfo.reason
                            )
                        }

                    }
                }
            }
        }
    }

    private fun paymentCompleted(data: PaymentResponse) {
        Timber.e("it.data.payType  ==>${data.payType}")
        when (data.payType) {
            PayTypeEnum.CREDIT.id, PayTypeEnum.USER_BALANCE.id, PayTypeEnum.MIXED_PAYMENT.id, PayTypeEnum.CASH_PAYMENT.id -> {
                viewModel.updateOrderMoreScreen(data.orderNo)
                context?.let { context ->
                    //临时记录一下打印的 已支付订单号 防止ws 收到重复打印
                    PrinterDeviceHelper.setOrderPrinter(data.orderNo)
                    viewModel.requestOrderedInfo(
                        data.orderNo, finishPay = true, context = context, isNeedPrint = true
                    )
                }
                if (PayTypeEnum.CREDIT.id == data.payType) {
                    PaymentSuccessDialog.showDialog(
                        parentFragmentManager, successText = getString(R.string.credit_success)
                    )
                } else {
                    PaymentSuccessDialog.showDialog(
                        parentFragmentManager
                    )
                }
                MixedPayDialog.dismissDialog(parentFragmentManager)
                PayDialog.dismissDialog(parentFragmentManager)
            }

            else -> {}
        }
        //更新订单状态
        viewModel.getOrderedInfo(data.orderNo!!)
        dismissProgress()
        binding?.btnPay?.setEnable(true)
    }

    private fun exportAgain() {
        binding?.apply {
            //已支付 结账-税务小票
            val orderInfo = viewModel.currentOrderedInfo
            //结账小票
            val casheirCheck = orderInfo?.isOrderSuccess() == true
            //预结小票
            val isPreSettlement =
                orderInfo?.payStatus == OrderedStatusEnum.UNPAID.id || orderInfo?.payStatus == OrderedStatusEnum.BE_CONFIRM.id

            pbOrderedInfo.isVisible = true
            viewModel.exportAgain(
                requireContext(), casheirCheck, isPreSettlement, printerViewModel
            ) { pdfFile ->
                pbOrderedInfo.isVisible = false
                // 本地文件
                if (pdfFile != null) {
                    try {
                        FileUtil.scanFile(requireActivity(), pdfFile)
                        val uri = FileProvider.getUriForFile(
                            requireContext(),
                            "${requireContext().packageName}.fileProvider",
                            pdfFile
                        )
                        val intent = Intent(Intent.ACTION_VIEW)
                        intent.setDataAndType(uri, "application/pdf")
                        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        requireContext().startActivity(intent)
                    } catch (e: Exception) {
                        requireActivity().runOnUiThread {
                            showToast(
                                getString(
                                    R.string.save_at_location, pdfFile.absolutePath
                                )
                            )
                        }
                    }
                }
            }
        }
    }

    private fun updateUnReadAndUnPrintBtn() {
        binding?.apply {
            btnUnRead.setBackgroundResource(if (isFilterUnRead) R.drawable.background_primary_color_radius_12dp else R.drawable.background_white_radius_12dp)
            tvUnRead.setTextColor(
                if (isFilterUnRead) requireContext().getColor(R.color.white) else requireContext().getColor(
                    R.color.black
                )
            )
            tvUnReadTitle.setTextColor(
                if (isFilterUnRead) requireContext().getColor(R.color.white) else requireContext().getColor(
                    R.color.black
                )
            )
            tvUnReadTitle.setCompoundDrawablesWithIntrinsicBounds(
                ContextCompat.getDrawable(
                    requireContext(),
                    if (isFilterUnRead) R.drawable.icon_unread_white else R.drawable.icon_unread
                ), null, null, null
            )

            btnUnPrint.setBackgroundResource(if (isFilterUnPrint) R.drawable.background_primary_color_radius_12dp else R.drawable.background_white_radius_12dp)
            tvUnPrint.setTextColor(
                if (isFilterUnPrint) requireContext().getColor(R.color.white) else requireContext().getColor(
                    R.color.black
                )
            )
            tvUnPrintTitle.setTextColor(
                if (isFilterUnPrint) requireContext().getColor(R.color.white) else requireContext().getColor(
                    R.color.black
                )
            )
            tvUnPrintTitle.setCompoundDrawablesWithIntrinsicBounds(
                ContextCompat.getDrawable(
                    requireContext(),
                    if (isFilterUnPrint) R.drawable.icon_unprint_white else R.drawable.icon_unprint
                ), null, null, null
            )
        }
    }

    private fun isCanChangeTable() {
        binding?.apply {
            if (viewModel.currentOrderedInfo?.isTakeOut() == true) {
                btnChangeTable.isVisible = false
                return@apply
            }
            //是否先付款
            val isPaymentInAdvance = MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance

            //kiosk 订单禁用订单换桌   非共享隐藏桌台也禁用订单换桌
            if (viewModel.currentOrderedInfo?.sourcePlatform == SourcePlatformEnum.Kiosk.id || (MainDashboardFragment.CURRENT_USER?.isTableService == false && MainDashboardFragment.CURRENT_USER?.isDisplayTable == false)) {
                //kiosk 禁用
                btnChangeTable.isVisible = false
//                tvTableID.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
                return@apply
            }


            if (isPaymentInAdvance == true) {
                //先付款
                if ((viewModel.currentOrderedInfo?.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || viewModel.currentOrderedInfo?.payStatus == OrderedStatusEnum.UNPAID.id) && (viewModel.currentOrderedInfo?.isPreOrder() != true)) {
                    btnChangeTable.isVisible = true
//                    val endDrawable = ContextCompat.getDrawable(
//                        requireContext(),
//                        R.drawable.ic_dropdown_primary
//                    )
//                    tvTableID.setCompoundDrawablesWithIntrinsicBounds(null, null, endDrawable, null)
                    return
                }
                btnChangeTable.isVisible = false
//                tvTableID.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
            } else {
                if ((viewModel.currentOrderedInfo?.payStatus == OrderedStatusEnum.TO_BE_CONFIRM.id || viewModel.currentOrderedInfo?.payStatus == OrderedStatusEnum.BE_CONFIRM.id || viewModel.currentOrderedInfo?.payStatus == OrderedStatusEnum.UNPAID.id) && (viewModel.currentOrderedInfo?.isReservation != true)) {
                    btnChangeTable.isVisible = true
//                    val endDrawable = ContextCompat.getDrawable(
//                        requireContext(),
//                        R.drawable.ic_dropdown_primary
//                    )
//                    tvTableID.setCompoundDrawablesWithIntrinsicBounds(null, null, endDrawable, null)
                    return
                }
            }
            btnChangeTable.isVisible = false
//            tvTableID.setCompoundDrawablesWithIntrinsicBounds(null, null, null, null)
        }
    }


    private fun initObserver() {
        viewModel.uiSplitOrderState.observe(viewLifecycleOwner) {
            if (it is ApiResponse.Error) {
                if (it.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                    binding?.apply {
                        viewModel.getOrderedList(
                            true,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem,
                            localOrderNo = viewModel.currentOrderedInfo?.orderNo,
                            isFilterUnRead = isFilterUnRead,
                            isFilterUnPrint = isFilterUnPrint,
                            consumerId = selecteAccount?.id
                        )
                    }
                }
                showToast(it.message ?: "")

            }
            if (it is ApiResponse.Success) {
                binding?.apply {
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem,
                        localOrderNo = viewModel.currentOrderedInfo?.orderNo,
                        isFilterUnRead = isFilterUnRead,
                        isFilterUnPrint = isFilterUnPrint,
                        consumerId = selecteAccount?.id
                    )
                }

            }
        }
        viewModel.paymentChannel.observe(viewLifecycleOwner) {
            binding?.apply {
//                addPayChannelRadioButton()
                // showPopupWindowPaymentMethod(btnPayNow)
            }
        }

        viewModel.uiListState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    pbOrderedList.isVisible = it

                }
                if (it.showEnd) {
                    if (it.isRefresh != false) {
                        layoutMenu.setVisibleGone(false)
                        contentCardView.setVisibleInvisible(false)
                        orderedAdapter?.replaceData(arrayListOf())
                        layoutEmptyList.root.setVisibleGone(true)
                        layoutDetailInfo.setVisibleInvisible(false)
                        actionScrollview.isVisible = false
                        clBottom.isVisible = false
                        layoutEmptyDetail.root.setVisibleGone(true)
                        btnPrint.isVisible = false
                        refreshLayout.finishRefresh()

                        btnCancelOrder.isVisible = false
                        btnWholeDiscount.isVisible = false
                        btnSingleDiscount.isVisible = false
                        btnOrderRefund.isVisible = false

                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                    pbOrderedInfo.isVisible = false
                }
                it.showError?.let { error ->
                    pbOrderedList.isGone = true
                    pbOrderedInfo.isVisible = false
                    refreshLayout.finishRefresh()
                    refreshLayout.finishLoadMore()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    pbOrderedList.isGone = true
                    layoutMenu.setVisibleGone(true)
                    contentCardView.setVisibleInvisible(true)
                    layoutEmptyList.root.setVisibleGone(false)
                    layoutEmptyDetail.root.setVisibleGone(false)
//                    actionScrollview.setVisibleGone(true)
                    layoutDetailInfo.setVisibleGone(true)
                    if (it.isRefresh != false) {
                        orderedAdapter?.replaceData(response.records)

                        var isNeedPrint = it.isNeedPrint
                        var indexOf = if (it.confirmOrderNo != null) {
                            isNeedPrint = true
                            //从点单下单过来的,如果
                            getSelectIndex(orderNo = it.confirmOrderNo, response)
                        } else if (it.localOrderNo != null) {
                            getSelectIndex(orderNo = it.localOrderNo, response)
                        } else {
                            0
                        }

                        if (indexOf == -1) {
                            indexOf = 0
                        }
                        Timber.e("it.isNeedPrint  ${it.isNeedPrint}  ${isNeedPrint}")
                        Timber.e("定位到index  :${indexOf}  ${it.currentOrderMore?.size}")

                        orderedAdapter?.setSelectFirst(indexOf)

                        if (!response.records.isNullOrEmpty()) {
                            context?.let { context ->
                                viewModel.getOrderedInfo(
                                    response.records[indexOf],
                                    context,
//                                    it.orderNo,
                                    currentOrderMore = it.currentOrderMore,
                                    isNeedPrint = isNeedPrint,
                                    finishPay = it.finishPay ?: false
                                )
                            }
                        } else {

                        }
                        refreshLayout.finishRefresh()
                    } else {
                        orderedAdapter?.addData(response.records)
                        refreshLayout.finishLoadMore()
                    }

                    clBottom.isVisible = orderedAdapter?.list?.isNotEmpty() == true
                }
            }
        }

        viewModel.uiInfoState.observe(viewLifecycleOwner) { state ->
            binding?.apply {
                state.orderedInfoResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            pbOrderedInfo.isVisible = true
//                            pbOrderedInfo2.isVisible = true
                        }

                        is ApiResponse.Error -> {
                            totalPrice = 0
                            totalVipPrice = BigDecimal.ZERO
                            pbOrderedInfo.isVisible = false
//                            pbOrderedInfo2.isVisible = false
                            showToast(it.message ?: "")
                            orderedInfoAdapter?.replaceData(arrayListOf())
                            viewOrderGiftList.setGiftPromotionData(
                                requireContext(), null
                            )
//                            orderInfoTypeMenuAdapter?.replaceData(arrayListOf())
                            layoutMenu.setVisibleGone(false)
                            contentCardView.setVisibleInvisible(false)
                            layoutEmptyDetail.root.setVisibleGone(true)
                            layoutDetailInfo.setVisibleGone(false)
                            actionScrollview.setVisibleGone(false)

                            if (it.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT) {
                                viewModel.getOrderedList(
                                    true,
                                    selectedValue,
                                    keyword = edtSearch.getSearchContent(),
                                    listTable = selectedTableItem,
                                    isFilterUnRead = isFilterUnRead,
                                    isFilterUnPrint = isFilterUnPrint,
                                    consumerId = selecteAccount?.id
                                )
                            } else {
                            }
                        }

                        is ApiResponse.Success -> {
                            layoutMenu.setVisibleGone(true)
                            contentCardView.setVisibleInvisible(true)
                            layoutEmptyDetail.root.setVisibleGone(false)
                            layoutDetailInfo.setVisibleGone(true)
                            actionScrollview.setVisibleGone(true)
                            pbOrderedInfo.isGone = true
//                            pbOrderedInfo2.isGone = true
                            //second screen-副屏
                            getOrderedScreen()?.setOrderInfo(
                                it.data, viewModel.getOrderUsableCouponList()
                            )

                            setOrderInfo(it.data)
                            orderedInfoAdapter?.replaceData(
                                OrderHelper.sortingGoodsWithMergeOrder(
                                    requireContext(),
                                    it.data.mergeOrderIds,
                                    it.data.acceptOrderIds,
                                    it.data.goods
                                ), it.data.refundGoodsJson, it.data
                            )

                            viewOrderGiftList.setGiftPromotionData(
                                requireContext(), null
                            )

                        }

                        else -> {

                        }
                    }
                }

                state.record?.let {
                    orderedAdapter?.updateStatus(it)
                }

            }
        }

        viewModel.uiSocketOrderedState.observe(viewLifecycleOwner) {
            it?.let {
                orderedAdapter?.updateRecord(it)
            }
        }

        viewModel.uiRequestState.observe(viewLifecycleOwner) { state ->
            binding?.apply {
                state.paymentResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
                            showProgress()
                            btnPay.setEnable(false)
                        }

                        is ApiResponse.Success -> {
                            paymentCompleted(it.data)
//                            Timber.e("it.data.payType  ==>${it.data.payType}")
//                            when (it.data.payType) {
//                                PayTypeEnum.ONLINE_PAYMENT.id -> {
//                                    PaymentQrDialog.showDialog(
//                                        parentFragmentManager,
//                                        it.data,
//                                        orderedScreen,
//                                        { res ->
////                                            SecondaryManager.setDefaultRoute()
////                                            SecondaryManagerV2.setDefaultRoute()
//                                            PrinterDeviceHelper.setOrderPrinter(it.data.orderNo)
//                                            viewModel.updateOrderMoreScreen(res.orderNo)
//                                            context?.let { context ->
//
//                                                viewModel.requestOrderedInfo(
//                                                    res.orderNo,
//                                                    finishPay = true,
//                                                    context = context,
//                                                    isNeedPrint = true
//                                                )
//                                            }
//                                            PaymentSuccessDialog.showDialog(
//                                                parentFragmentManager,
//                                                orderedInfo = res,
//                                                orderedScreen
//                                            ) {
//                                                //update secondary
////                                                SecondaryManager.setDefaultRoute()
////                                                SecondaryManagerV2.setDefaultRoute()
//                                                getOrderedScreen()?.showOrderedInfo()
//                                                getOrderedScreen()?.setOrderInfo(
//                                                    viewModel.currentOrderedInfo,
//                                                    viewModel.getOrderUsableCouponList()
//                                                )
//                                            }
//                                        }, {
////                                            SecondaryManager.setDefaultRoute()
////                                            SecondaryManagerV2.setDefaultRoute()
//
//                                            //update secondary
//                                            getOrderedScreen()?.showOrderedInfo()
//                                            getOrderedScreen()?.setOrderInfo(
//                                                viewModel.currentOrderedInfo,
//                                                viewModel.getOrderUsableCouponList()
//                                            )
//                                        })
//                                }
//
//                                PayTypeEnum.CASH_PAYMENT.id -> {
//                                    viewModel.updateOrderMoreScreen(it.data.orderNo)
//                                    context?.let { context ->
//                                        //临时记录一下打印的 已支付订单号 防止ws 收到重复打印
//                                        PrinterDeviceHelper.setOrderPrinter(it.data.orderNo)
//                                        viewModel.requestOrderedInfo(
//                                            it.data.orderNo,
//                                            finishPay = true,
//                                            context = context,
//                                            isNeedPrint = true
//                                        )
//                                    }
//                                    PaymentSuccessDialog.showDialog(parentFragmentManager)
//                                }
//
//                                PayTypeEnum.USER_BALANCE.id -> {
//                                    viewModel.updateOrderMoreScreen(it.data.orderNo)
//                                    context?.let { context ->
//                                        //临时记录一下打印的 已支付订单号 防止ws 收到重复打印
//                                        PrinterDeviceHelper.setOrderPrinter(it.data.orderNo)
//                                        viewModel.requestOrderedInfo(
//                                            it.data.orderNo,
//                                            finishPay = true,
//                                            context = context,
//                                            isNeedPrint = true
//                                        )
//                                    }
//                                    PaymentSuccessDialog.showDialog(parentFragmentManager)
//                                }
//
//                                else -> {}
//                            }
//                            //更新订单状态
//                            viewModel.getOrderedInfo(it.data.orderNo!!)
//                            dismissProgress()
//                            btnPay.setEnable(true)
                        }

                        is ApiResponse.Error -> {
                            pbConfirm.isVisible = false
                            btnPay.setEnable(true)
                            dismissProgress()
                            showToast(it.message ?: "")
                            if (it.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT || it.errorCode == ORDER_INFO_CHANGE) {
                                viewModel.getOrderedList(
                                    true,
                                    selectedValue,
                                    keyword = edtSearch.getSearchContent(),
                                    listTable = selectedTableItem,
                                    isFilterUnRead = isFilterUnRead,
                                    isFilterUnPrint = isFilterUnPrint,
                                    consumerId = selecteAccount?.id
                                )
                            } else {
                                if (viewModel.currentOrderedInfo?.orderNo != null) {
                                    viewModel.getOrderedInfo(viewModel.currentOrderedInfo?.orderNo!!)
                                }
                            }
                        }

                        else -> {

                        }
                    }
                }

                state.refundResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
//                            pbOrderRefund.isVisible = true
                        }

                        is ApiResponse.Success -> {
                            showToast(getString(R.string.refund_successfully))
//                            pbOrderRefund.isGone = true
                        }

                        is ApiResponse.Error -> {
//                            pbOrderRefund.isGone = true
                            showToast(it.message ?: "")
                        }

                        else -> {

                        }
                    }
                }
            }
        }

        viewModel.uiSaveRecordState.observe(viewLifecycleOwner) {
            it.showLoading?.let {
                showProgress()
            }
            it.showSuccess?.let {
                val currentFragment = parentFragmentManager.fragments.firstOrNull()
                val bundle = Bundle()
                bundle.putInt(
                    ORDERED_FRAGMENT_BUNDLE_DATA, viewModel.currentOrderedInfo?.diningStyle ?: 0
                )
                if (currentFragment is MainDashboardFragment) {
                    currentFragment.replaceFragmentFromOtherFragment(
                        FeatureMenuEnum.ORDER.id, MenuOrderFragment(), bundle
                    )
                }
            }
        }

        viewModel.uiInfoRequestState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.baseBooleanResponse?.let {
                    when (it) {
                        is ApiResponse.Loading -> {
//                            pbOrderedInfo2.isVisible = true
                            pbOrderedInfo.isVisible = true
                            btnCancelOrder.isEnabled = false
                            btnPay.isEnabled = false
                        }

                        is ApiResponse.Success -> {
//                            pbOrderedInfo2.isVisible = false
                            pbOrderedInfo.isVisible = false
                            btnCancelOrder.isEnabled = true

                            btnPay.isEnabled = true
                        }

                        is ApiResponse.Error -> {
//                            pbOrderedInfo2.isVisible = false
                            pbOrderedInfo.isVisible = false
                            showToast(it.message ?: "")
                            btnCancelOrder.isEnabled = true
                            btnPay.isEnabled = true
                            if (it.errorCode == GOODS_HAS_BEEN_MERGE_OR_SPLIT || it.errorCode == ORDER_INFO_CHANGE) {
                                viewModel.getOrderedList(
                                    true,
                                    selectedValue,
                                    keyword = edtSearch.getSearchContent(),
                                    listTable = selectedTableItem,
                                    isFilterUnRead = isFilterUnRead,
                                    isFilterUnPrint = isFilterUnPrint,
                                    consumerId = selecteAccount?.id
                                )
                            }
                        }

                        else -> {

                        }
                    }
                }
            }
        }

        viewModel.uiChangeTableByOrderState.observe(viewLifecycleOwner) { state ->
            binding?.apply {
                state.isOver?.let {
                    SwitchTableByOrderDialog.dismissDialog(parentFragmentManager)
                }
                state.oldTableUUid?.let {
                    //更新列表里面
                    state.newTable?.let {
                        orderedAdapter?.updateOrdersTable(state.oldTableUUid, it)
                    }
                }

            }
        }
    }

    private fun FragmentOrderedBinding.getSelectIndex(
        orderNo: String? = null, response: OrderedTotalResponse
    ): Int {
        var indexOf = 0
        if (!orderNo.isNullOrEmpty()) {
            indexOf = response.records?.indexOf(OrderedRecord(orderNo = orderNo)) ?: 0
//            if (indexOf != -1) {
//                orderListRecyclerView.postDelayed({
//                    orderListRecyclerView.scrollToPosition(indexOf)
//                }, 500)
//            }
            if (indexOf == -1) {
                indexOf = 0
            } else {
                orderListRecyclerView.postDelayed({
                    orderListRecyclerView.scrollToPosition(indexOf)
                }, 500)
            }
        }
//        if (!orderMoreNo.isNullOrEmpty()) {
//            indexOf =
//                response.records?.indexOf(OrderedRecord(orderNo = orderMoreNo)) ?: 0
//            if (indexOf == -1) {
//                indexOf = 0
//            } else {
//                orderListRecyclerView.postDelayed({
//                    orderListRecyclerView.scrollToPosition(indexOf)
//                }, 500)
//            }
//        }
        return indexOf
    }


    @SuppressLint("SetTextI18n")
    private fun FragmentOrderedBinding.setOrderInfo(ordered: OrderedInfoResponse?) {
        ordered?.let {
            layoutOrderInfo.apply {
                updateReceiveView(ordered)
                llOrderTable.isVisible = true
                tvOrderTable.text = it.tableName
                if (it.isTakeOut()) {
                    llTakeOutPlatform.isVisible = true
                    tvTakeOutPlatform.text = it.deliveryPlatformName
                    llTakeOutId.isVisible = true
                    tvTakeOutId.text = it.deliveryOrderNo
                } else {
                    llTakeOutPlatform.isVisible = false
                    llTakeOutId.isVisible = false
                }
                if (it.sourcePlatform == SourcePlatformEnum.Kiosk.id) {
                    tvOrderTable.text = getString(R.string.kiosk).uppercase()
                }

                llPickUpNo.isVisible = !it.pickupCode.isNullOrEmpty()
                tvPickUpNo.text = it.pickupCode
                llCustomerTitle.isVisible = true
                ivEditCustomer.isVisible = false

                llCustomerName.isVisible = !it.getLastCustomerName().isNullOrEmpty()
                tvCustomerName.text = it.getLastCustomerName()

                llCustomerPhone.isVisible = it.getShowPhone().isNotEmpty()
                tvCustomerPhone.text = it.getShowPhone()

//                      it.customerInfoVo?.getMobilePhoneDisplay()

                llPeople.isVisible = !(it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString()
                    .isNullOrEmpty() && (it.customerInfoVo?.diningNumber ?: it.peopleNum) != 0
                tvCustomerNum.text =
                    (it.customerInfoVo?.diningNumber ?: it.peopleNum)?.toString() ?: ""

                tvOrderNo.text = it.orderNo

                llInvoiceNumber.isVisible = !it.invoiceNumber.isNullOrEmpty()
                tvInvoiceNumber.text = it.invoiceNumber


                tvOrderTime.text = it.createTime?.formatDate()
                llDiningTime.isVisible = !(it.getDingTime()).isNullOrEmpty()
                tvDiningTime.text = (it.getDingTime())?.formatDate()
                context?.let { context ->
                    tvOrderType.text = it.getDiningStyleStr(context)
                    llOrderStatus.isVisible = true
                    tvOrderStatus.text = it.payStatus?.getPayText(context)
                    tvOrderStatus.setTextColor(
                        ContextCompat.getColor(
                            context, it.payStatus?.getPayTypeColor() ?: R.color.ordered_cancel_color
                        )
                    )

                    tvOrderBy.text = it.sourcePlatform?.getSourcePlatform(context)
                }

                tvOrderRemark.text =
                    if (it.note.isNullOrEmpty()) getString(R.string.none) else it.getFinalNote()

                llAntiSettlementReason.isVisible = !it.reverseReason.isNullOrEmpty()
                tvOrderAntiSettlement.text = it.reverseReason




                if (!it.isAfterPayStatus()) {
                    //支付前 总计要自己再计算一遍优惠券的金额
                    it.getTotalVipPriceBySelf()
                    it.getTotalPriceBySelf()
                }

                /**
                 * 小计
                 */
                val subTotalPrice = it.getSubTotal()

                /**
                 * 服务费
                 */
                val serviceFeePrice = it.getRealServiceFeePrice()

                /**
                 * 会员服务费
                 */
                val vipServiceFeePrice = it.getVipServiceFeePrice()

                /**
                 * 增值税
                 */
                val vatPrice = it.getRealVatPrice()

                /**
                 * 会员增值税增值税
                 */
                val vipVatPrice = it.getVipVat()

                /**
                 * 总计
                 */
                totalPrice = it.getRealPayPrice()
                /**
                 * 会员总计
                 */
                totalVipPrice = it.getFinalTotalVipPrice()


                tvPackingAmount.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    it.getPackFee(),
                    it.isKhr()
                )

                llPackPrice.isVisible = it.getPackFee() > 0

                //小计
                tvSubtotal.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                    subTotalPrice,
                    it.isKhr()
                )

                titleVat.text = "${getString(R.string.vat)}(${it.price?.getVatPercentage()}%)"
                //增值税
                tvVat.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio, vatPrice, it.isKhr()
                )

                Timber.e("serviceFeePrice  $serviceFeePrice")

                //服务费
                tvServiceFee.text = serviceFeePrice.priceFormatTwoDigitZero2()

                if (it.isTakeOut()) {
                    llCommission.isVisible = true
                    tvCommissionPrice.text = "-${
                        FoundationHelper.getPriceStrByUnit(
                            it.conversionRatio ?: FoundationHelper.conversionRatio,
                            it.price?.getCommissionToLong() ?: 0L,
                            it.isKhr()
                        )
                    }"
                } else {
                    llCommission.isVisible = false
                }

                //总计2
                tvTotalPrice2.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio, totalPrice, it.isKhr()
                )
                tvTotalKhrPrice2.text = "៛${
                    FoundationHelper.usdConverToKhr(
                        it.conversionRatio ?: FoundationHelper.conversionRatio!!, totalPrice
                    ).decimalFormatZeroDigit()
                }"

                //总计1
                tvTotalPrice.text = FoundationHelper.getPriceStrByUnit(
                    it.conversionRatio ?: FoundationHelper.conversionRatio, totalPrice, it.isKhr()
                )
                tvTotalKhrPrice.isVisible = true
                tvTotalKhrPrice.text = "៛${
                    FoundationHelper.usdConverToKhr(
                        it.conversionRatio ?: FoundationHelper.conversionRatio!!, totalPrice
                    ).decimalFormatZeroDigit()
                }"

                if (it.isTakeOut() && it.isKhr()) {
                    tvTotalKhrPrice.isVisible = false
                }


                //会员价
                tvVipPrice.text = totalVipPrice.priceFormatTwoDigitZero2()

                val isShowVip = it.isShowVipPrice()
                tvVipPrice.isVisible = isShowVip

                //=======优惠活动金额================
                if (it.couponActivityList.isNullOrEmpty()) {
                    llDiscountActivity.isVisible = false
                } else {
                    btnDiscountActivityPriceCue.isVisible = true
                    if (it.couponActivityList?.size == 1) {
                        tvDiscountActivityTitle.text =
                            it.couponActivityList?.firstOrNull()?.activityLabelName
                    } else {
                        tvDiscountActivityTitle.text = getString(R.string.discount_activity)
                    }
                    if (it.isAfterPayStatus()) {
                        //如果是余额支付 取会员价
                        tvDiscountActivityAmount.text =
                            "-${it.getTotalCouponActivityAmount()?.priceFormatTwoDigitZero2()}"
                    } else {
                        val discountActivityUnWeightList =
                            it.couponActivityList?.filter { it.weightMark == true } ?: listOf()
                        if (discountActivityUnWeightList.isNullOrEmpty()) {
                            tvDiscountActivityAmount.text =
                                "-${it.getTotalCouponActivityAmount()?.priceFormatTwoDigitZero2()}"
                        } else {
                            tvDiscountActivityAmount.text = getString(R.string.to_be_confirmed)
                        }
                    }
                    llDiscountActivity.isVisible = true
                }
                //==============================


                //===================优惠券相关=================

                llCoupon.isVisible = false
                val isHasNeedProcess = ordered.isHasNeedProcess()
                tvCoupon.text = it.getCouponDesc(
                    requireContext(),
                    viewModel.getOrderUsableCouponList().isEmpty(),
                    isHasNeedProcess
                )
                tvViewCouponGiftGood.isVisible = false
//                iconCouponArrow.isVisible = true
                Timber.e("coupon: ${it.getCurrentCoupon()?.toJson()}")

                if (it.getCurrentCoupon() != null) {
                    llCoupon.isVisible = true
                    if (it.getZsCouponValid()) {
                        tvViewCouponGiftGood.isVisible = true
                    }
                }

                //======================优惠券================================

                if (isShowVip) {
//                    tvOriginalPrice.isVisible = false
                    llServiceFee.isVisible =
                        serviceFeePrice > 0 || vipServiceFeePrice > BigDecimal.ZERO
                    llVat.isVisible = vatPrice > 0 || vipVatPrice > BigDecimal.ZERO

                    if (it.isAfterPayStatus()) {
                        //如果是支付后的状态
                        llServiceFee.isVisible = serviceFeePrice > 0
                        llVat.isVisible = vatPrice > 0
                    }
                } else {
                    tvVipPrice.isVisible = false
                    llServiceFee.isVisible = serviceFeePrice > 0
                    llVat.isVisible = vatPrice > 0
                }

                //判断是否有新版整单减免
                llDiscount.isVisible = false
                llDiscountAmount.isVisible = false
                btnDiscountCue.isVisible = false
                btnDiscountAmountCue.isVisible = false
                if (it.isSetWholeDiscount()) {
                    //设置的是后台配配置的整单减免
                    if (it.discountReduceActivity != null) {
                        if (it.discountReduceActivity?.isPercentDiscount() == true) {
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount().times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvDiscountTitle.text =
                                "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        } else if (it.discountReduceActivity?.isFixedAmount() == true) {
                            //设置的是整单减免
                            llDiscountAmount.isVisible = true
                            val title = getString(R.string.discounts2)
                            val content = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceDollar().times(BigDecimal(100))
                                        .toLong(),
                                    it.isKhr()
                                )
                            }"
                            tvDiscountAmount.text = content
                            tvDiscountAmountTitle.text = title
                        }
                    } else {
                        if (it.getWholePercentDiscount() > BigDecimal.ZERO) {
                            //设置的是整单百分比折扣
                            llDiscount.isVisible = true
                            tvDiscount.text = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getWholePercentDiscountAmount().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"
                            tvDiscountTitle.text =
                                "${getString(R.string.discounts2)}(${it.getWholePercentDiscountStr()})"
                        }

                        if (it.isSetCustomizeReduce()) {
                            //自定义整单减免
                            llDiscountAmount.isVisible = true
                            var content = "-${
                                FoundationHelper.getPriceStrByUnit(
                                    it.conversionRatio ?: FoundationHelper.conversionRatio,
                                    it.getNormalWholeItemReduceDollar().times(BigDecimal(100))
                                        .toLong(),
                                    it.wholeDiscountReduce?.isCustomizeKhr() == true
                                )
                            }"

                            if (it.getNormalWholeReduceKhr() > BigDecimal.ZERO || it.getVipWholeReduceKhr() > BigDecimal.ZERO) {
                                content =
                                    "-៛${it.getNormalWholeItemReduceKhr().decimalFormatZeroDigit()}"
                            }

                            tvDiscountAmount.text = content
                            tvDiscountAmountTitle.text = getString(R.string.discounts2)
                            if (it.isAfterPayStatus()) {
                                //如果支付完了以后 有设置值才显示
                                if (it.getNormalWholeReduceDollar() == BigDecimal.ZERO && it.getNormalWholeReduceKhr() == BigDecimal.ZERO) {
                                    llDiscountAmount.isVisible = false
                                }
                            }
                        }
                    }
                    btnDiscountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                    btnDiscountAmountCue.isVisible = it.getWholeDiscountReason().isNotEmpty()
                }


                //normal
                btnRemark.isVisible = false
                llPaymentLayout.isGone = true
                btnSubmit.isVisible = false
                btnOrderRefund.isGone = true
                llActualReceiveAmount.isGone = true
                llVatRefundAmount.isGone = true
                llPartialRefundAmount.isGone = true
                llTotalPrice2.isGone = true
                llRefundTime.isGone = true
                llPaymentTime.isGone = it.payTime.isNullOrEmpty()
                tvPaymentTime.text = it.payTime?.formatDate()
                llPaymentMethod1.isVisible = false
                llPaymentMethod2.isVisible = false
                llChange.isVisible = false
                llPaymentMethodPayAmount1.isVisible = false
                llPaymentMethodPayAmount2.isVisible = false
                llTotalPrice.isVisible = true
                llRefundAmount.isGone = true
                llCancelTime.isGone = true
                llCancelReason.isGone = true
                llPartialRefundPackFee.isVisible = false
                llPartialRefundServiceFee.isVisible = false
                btnWholeDiscount.isVisible = false
                btnSingleDiscount.isVisible = false
                btnBackMenu.isVisible = false
                btnCoupon.isVisible = false
                actionScrollview.isVisible = true
                btnAntiSettlement.isVisible = false
                btnTotalPriceDetail.isVisible = false
                btnMerge.isVisible = false
                btnSplit.isVisible = false
                btnCancelDish.isVisible = false
                btnTmpGood.isVisible = false
                btnOrderMore.isVisible = false
                btnCancelOrder.isVisible = false
                ivEditTakeOutId.isVisible = false
                llRefundPayType.isVisible = false
                btnCredit.isVisible = false
                btnCancelCredit.isVisible = false

                llUserAccount.isVisible = false
                llUserName.isVisible = false
                llCreditReason.isVisible = false
                llCancelCreditReason.isVisible = false

                llRepaymentTime.isVisible = !it.repaymentTime.isNullOrEmpty()
                tvRepaymentTime.text = it.repaymentTime?.formatDate()

                btnPrint.isVisible =
                    (it.isOrderSuccess() || it.payStatus == OrderedStatusEnum.BE_CONFIRM.id || it.payStatus == OrderedStatusEnum.UNPAID.id)

                btnExport.isVisible =
                    (it.isOrderSuccess() || it.payStatus == OrderedStatusEnum.BE_CONFIRM.id || it.payStatus == OrderedStatusEnum.UNPAID.id)

                // ticket bug-view-2101 || it.payStatus == OrderedStatusEnum.BE_CONFIRM.id)


//                val isPaymentInAdvance = MainDashboardFragment.CURRENT_USER?.isPaymentInAdvance

                isShowBtnWeigh()
                isShowBtnSetTimePrice()

                //判断是否有挂账权限
                val isCanCredit = MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.CREDIT.type) == true && !ordered.isPreOrder() && !ordered.isTakeOut()
//                isCanCancelDish()
                when (it.payStatus) {
                    //挂账-未支付
                    OrderedStatusEnum.CREDIT_UNPAID.id -> {
                        updatePaymentMethod(ordered)
                        btnCancelCredit.isVisible = isCanCredit

                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                    }
                    //挂账-已支付
                    OrderedStatusEnum.CREDIT_PAID.id -> {
                        updatePaymentMethod(ordered)
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                    }
                    //待支付
                    OrderedStatusEnum.UNPAID.id -> {

                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                        //取消挂账
                        llCancelCreditReason.isVisible = !it.cancelCreditReason.isNullOrEmpty()
                        tvCancelCreditReason.text = it.cancelCreditReason


                        btnCredit.isVisible = isCanCredit && (it.realPrice ?: 0L) > 0L
                        ivEditCustomer.isVisible = true
                        btnRemark.isVisible = true

                        //un paid
                        llPaymentLayout.isVisible = true
                        btnOrderRefund.isVisible = false

                        //kiosk 和通用桌不能合单
                        btnMerge.isVisible = !it.isFromKiosk() && !it.isUniversalTable()
                        btnSplit.isVisible = !it.mergeOrderIds.isNullOrEmpty()

                        /**
                         * kiosk 的待支付单先不能加购
                         */
                        btnOrderMore.isVisible = !it.isFromKiosk()
                        btnTmpGood.isVisible = !it.isFromKiosk()


                        showCancelOrderBtn()
                        showCancelDishBtn()

                        /**
                         * 预定待支付不要加购
                         */
                        if (it.isPreOrder()) {
                            btnOrderMore.isVisible = false
                            btnCancelDish.isVisible = false
                            btnTmpGood.isVisible = false
                        }


                        btnCoupon.isVisible = true
                        btnTotalPriceDetail.isVisible = true

                        showDiscountBtn(ordered)
                        isRemarkBtnEnable(ordered)
                        val hasUnWeight = showTobeProcess(ordered)

                        if (it.isPreOrder()) {
                            btnWholeDiscount.isVisible = false
                            btnSingleDiscount.isVisible = false
                        }

//                        btnPay.setEnableWithAlpha(!hasUnWeight)

                    }
                    //已支付
                    OrderedStatusEnum.PAID.id -> {

                        llCustomerTitle.isVisible =
                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        showBtnAntiSettlement(it)
                        llAntiSettlementReason.isVisible = false
                        showBtnRefundBtn(it)
                        //paid
                        llPaymentLayout.isVisible = false

                        updatePaymentMethod(ordered)
//                        llPaymentMethod.isVisible = true
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        btnBackMenu.isVisible = true
                        if (viewModel.currentOrderedInfo?.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                        if (it.isTakeOut()) {
                            ivEditTakeOutId.isVisible = true
                            btnAntiSettlement.isVisible = false
                        }

                        handlerAfterOrderCancel(ordered.orderNo)
                    }
                    //部分退款
                    OrderedStatusEnum.PARTIAL_REFUND.id -> {
                        llCustomerTitle.isVisible =
                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        llAntiSettlementReason.isVisible = false
                        showBtnRefundBtn(it)
                        //partial refund
                        llTotalPrice.isGone = true
                        llTotalPrice2.isVisible = true
                        updatePaymentMethod(ordered)
//                        llPaymentMethod.isVisible = true
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false

                        llRefundTime.isVisible = true
                        tvRefundTime.text = it.refundDateTime?.formatDate()

                        btnBackMenu.isVisible = true
                        llRefundPayType.isVisible = true
                        if (it.isPayByMix()) {
                            if (it.oneTimeRefund == true) {
                                tvRefundPayType.text =
                                    "${getString(R.string.balance)}\n${getString(R.string.offline_refund)}"
                            } else {
                                tvRefundPayType.text = getString(R.string.offline_refund)
                            }

                        } else {
                            tvRefundPayType.text = ordered?.getRefundType(requireContext())
                        }

                        llActualReceiveAmount.isVisible = true
                        tvActualReceiveAmount.text = FoundationHelper.getPriceStrByUnit(
                            it.conversionRatio ?: FoundationHelper.conversionRatio,
                            (totalPrice - (it.refundPrice ?: 0)),
                            it.isKhr()
                        )
//                            (totalPrice - (it.refundPrice
//                                ?: 0)).priceFormatTwoDigitZero2()

                        val vatRefundAmount = it.getVatRefundAmount()
                        llVatRefundAmount.isVisible = vatRefundAmount > 0
                        tvVatRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                vatRefundAmount,
                                it.isKhr()
                            )
                        }"
                        //"-${vatRefundAmount.priceFormatTwoDigitZero2()}"

                        val packRefundAmount = it.getPackRefundAmount()
                        llPartialRefundPackFee.isVisible = packRefundAmount > 0
                        tvPartialRefundPackFee.text =
                            "-${packRefundAmount.priceFormatTwoDigitZero2()}"

                        llPartialRefundAmount.isVisible = true
                        tvPartialRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                it.getPartialRefundAmount(),
                                it.isKhr()
                            )
                        }"
//                            "-${it.getPartialRefundAmount().priceFormatTwoDigitZero2()}"
                        if (!it.isTakeAway()) {
                            val serviceFeeRefundAmount = it.getServiceFeeRefundAmount()
                            llPartialRefundServiceFee.isVisible = serviceFeeRefundAmount > 0
                            tvPartialRefundServiceFee.text =
                                "-${serviceFeeRefundAmount.priceFormatTwoDigitZero2()}"
                        }


                        if (viewModel.currentOrderedInfo?.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }

                        if (it.isTakeOut() && it.isKhr()) {
                            tvTotalKhrPrice2.isVisible = false
                        } else {
                            tvTotalKhrPrice2.isVisible = true
                        }
                    }
                    //全额退款
                    OrderedStatusEnum.FULL_REFUND.id -> {
                        llCustomerTitle.isVisible =
                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        llAntiSettlementReason.isVisible = false

                        actionScrollview.isVisible = false
                        btnOrderRefund.isVisible = false
                        llPaymentLayout.isVisible = false
                        llTotalPrice2.isVisible = true
                        llTotalPrice.isGone = true
                        updatePaymentMethod(ordered)
//                        llPaymentMethod.isVisible = true
                        llRefundTime.isVisible = true
                        tvRefundTime.text = it.refundDateTime?.formatDate()
                        //refund
                        llRefundAmount.isVisible = true
                        tvRefundAmount.text = "-${
                            FoundationHelper.getPriceStrByUnit(
                                it.conversionRatio ?: FoundationHelper.conversionRatio,
                                it.refundPrice,
                                it.isKhr()
                            )
                        }"
//                        "-${it.refundPrice?.priceFormatTwoDigitZero2()}"

                        tvVipPrice.isVisible = false

                        btnBackMenu.isVisible = true
                        llRefundPayType.isVisible = true
                        if (it.isPayByMix()) {
                            if (it.oneTimeRefund == true) {
                                tvRefundPayType.text =
                                    "${getString(R.string.balance)}\n${getString(R.string.offline_refund)}"
                            } else {
                                tvRefundPayType.text = getString(R.string.offline_refund)
                            }

                        } else {
                            tvRefundPayType.text = ordered?.getRefundType(requireContext())
                        }

                        if (viewModel.currentOrderedInfo?.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }

                        if (it.isTakeOut() && it.isKhr()) {
                            tvTotalKhrPrice2.isVisible = false
                        } else {
                            tvTotalKhrPrice2.isVisible = true
                        }
                    }
                    //已取消
                    OrderedStatusEnum.CANCEL_ORDER.id -> {
                        llUserAccount.isVisible = !it.creditConsumerTelephone.isNullOrEmpty()
                        tvUserAccount.text = it.creditConsumerTelephone
                        llUserName.isVisible = !it.creditConsumerNickname.isNullOrEmpty()
                        tvUserName.text = it.creditConsumerNickname

                        //挂账备注
                        llCreditReason.isVisible = !it.creditReason.isNullOrEmpty()
                        tvCreditReason.text = it.creditReason
                        //取消挂账
                        llCancelCreditReason.isVisible = !it.cancelCreditReason.isNullOrEmpty()
                        tvCancelCreditReason.text = it.cancelCreditReason




                        llCustomerTitle.isVisible =
                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        actionScrollview.isVisible = false
                        //canceled
//                        btnOrderMore.isVisible = false
//                        btnCancelDish.isVisible = false
//                        btnCancelOrder.isVisible = false
                        btnOrderRefund.isVisible = false
                        llPaymentLayout.isVisible = false
                        llCancelTime.isVisible = !it.cancelTime.isNullOrEmpty()
                        tvCancelTime.text = it.cancelTime?.formatDate()


                        val cancelReason = it.cancelReason ?: ""
                        llCancelReason.isVisible = cancelReason.isNotEmpty()

                        tvCancelReason.originalText = ""
                        tvCancelReason.setOpenSuffixColor(requireContext().getColor(R.color.primaryColor))
                        tvCancelReason.setOpenSuffix(getString(R.string.review))
                        tvCancelReason.postDelayed({
                            Timber.e("tvCancelReason2.measuredWidth ${tvCancelReason.measuredWidth}")
                            tvCancelReason.initWidth(tvCancelReason.measuredWidth)
                            tvCancelReason.setMaxLines(1)
                            tvCancelReason.originalText = cancelReason
                        }, 300)
                        tvCancelReason.setOnClickListener {
                            CancelReasonDialog.showDialog(
                                fragmentManager = parentFragmentManager, cancelReason
                            )
                        }

                        btnBackMenu.isVisible = true


                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false

                        showTobeProcess(ordered)

                        handlerAfterOrderCancel(ordered.orderNo)
                        if (viewModel.currentOrderedInfo?.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                    }
                    //已确认
                    OrderedStatusEnum.BE_CONFIRM.id -> {
                        btnCredit.isVisible = isCanCredit && (it.realPrice ?: 0L) > 0L
                        ivEditCustomer.isVisible = true
                        //confirm
                        llPaymentLayout.isVisible = true
                        btnOrderRefund.isVisible = false

                        btnRemark.isVisible = true

                        //kiosk 和通用装不能合单
                        btnMerge.isVisible = !it.isFromKiosk() && !it.isUniversalTable()
                        btnSplit.isVisible = !it.mergeOrderIds.isNullOrEmpty()


                        showCancelDishBtn()
                        showCancelOrderBtn()

                        btnOrderMore.isVisible = true
                        btnTmpGood.isVisible = true
                        btnCoupon.isVisible = true
                        btnTotalPriceDetail.isVisible = true
                        //后付款如果是外带和通用桌订单 隐藏加购

                        showDiscountBtn(ordered)
                        isRemarkBtnEnable(ordered)
                        val hasUnWeight = showTobeProcess(ordered)

                        if (it.isPreOrder()) {
                            btnWholeDiscount.isVisible = false
                            btnSingleDiscount.isVisible = false
                        }

//                        btnPay.setEnableWithAlpha(!hasUnWeight)
                    }
                    //预定
                    OrderedStatusEnum.PREORDER.id -> {
                        llCustomerTitle.isVisible =
                            llCustomerName.isVisible || llCustomerPhone.isVisible || llPeople.isVisible
                        showBtnAntiSettlement(it)
                        llAntiSettlementReason.isVisible = false
                        showBtnRefundBtn(it)


                        llPaymentLayout.isVisible = false
                        updatePaymentMethod(ordered)
//                        llPaymentMethod.isVisible = true
                        tvVipPrice.isVisible = false
//                        tvOriginalPrice.isVisible = false
                        btnBackMenu.isVisible = true

                        if (viewModel.currentOrderedInfo?.getCurrentCoupon() == null) {
                            llCoupon.isVisible = false
                        }
                        handlerAfterOrderCancel(ordered.orderNo)
                    }
                    //待确认
                    OrderedStatusEnum.TO_BE_CONFIRM.id -> {
//                        btnCredit.isVisible = isCanCredit && (it.realPrice ?: 0L) > 0L

                        ivEditCustomer.isVisible = true
                        btnRemark.isVisible = true

                        //待确认没支付功能
                        llPaymentLayout.isVisible = false
                        btnOrderRefund.isVisible = false

                        showCancelDishBtn()
                        showCancelOrderBtn()

                        btnSubmit.isVisible = true

                        btnOrderMore.isVisible = true
                        btnTmpGood.isVisible = true
                        btnCoupon.isVisible = true

                        btnTotalPriceDetail.isVisible = true

                        showTobeProcess(ordered)
                    }
                }

                isCanChangeTable()
            }
        }
    }

    private fun updatePaymentMethod(orderedInfo: OrderedInfoResponse?) {
        binding?.apply {
            context?.let { context ->
                layoutOrderInfo.apply {
                    if (orderedInfo?.isPayByMix() == true) {
                        //如果是混合支付
                        llPaymentMethod1.isVisible = true
                        llPaymentMethod2.isVisible = true
                        llPaymentMethodPayAmount1.isVisible = true
                        llPaymentMethodPayAmount2.isVisible = true
                        paymentMethod1.text = getString(R.string.payment_method_1)
                        tvPaymentMethod1.text = getString(R.string.pay_by_balance)

                        tvPaymentMethodPayAmount1.text =
                            orderedInfo.balancePayAmount?.priceFormatTwoDigitZero1()

                        paymentMethod2.text = getString(R.string.payment_method_2)
                        tvPaymentMethod2.text = orderedInfo?.getOfflinePayMethod(context)
                        if (orderedInfo?.isCash() == true) {
                            paymentMethodPayAmount2.text = getString(R.string.cash_collection)
                            tvPaymentMethodPayAmount2.text = orderedInfo.getReceiveAmountToTicket()
                        } else {
                            paymentMethodPayAmount2.text = getString(R.string.pay_amount)
                            tvPaymentMethodPayAmount2.text =
                                orderedInfo.offlinePayAmount?.priceFormatTwoDigitZero1()
                        }
                    } else {
                        llPaymentMethod1.isVisible = true
                        paymentMethod1.text = getString(R.string.payment_method)
                        tvPaymentMethod1.text = orderedInfo?.getPaymentMethod(context)
                        if (orderedInfo?.isCash() == true) {
                            llPaymentMethodPayAmount2.isVisible = true
                            paymentMethodPayAmount2.text = getString(R.string.cash_collection)
                            tvPaymentMethodPayAmount2.text = orderedInfo.getReceiveAmountToTicket()
                        }
                    }

                    if (orderedInfo?.isCash() == true) {
                        //如果是现金支付 显示收取现金 和找零
                        llChange.isVisible = true
                        tvChange.text = orderedInfo?.getChangeAmountToTicket()
                    }
                }
            }
        }
    }

    /**
     * 显示单品折扣按钮
     *
     */
    private fun showDiscountBtn(orderedInfo: OrderedInfoResponse?) {
        orderedInfo?.let {
            binding?.apply {
                if (viewModel.currentOrderedInfo?.isAfterPayStatus() == false && viewModel.currentOrderedInfo?.isToBeConfirmState() == false && viewModel.currentOrderedInfo?.isPreOrder() == false) {
                    val hasUnWeight = showTobeProcess(it)
                    if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                            ?.contains(PermissionEnum.CUSTOMIZE_DISCOUNT.type) == true || MainDashboardFragment.CURRENT_USER?.getPermissionList()
                            ?.contains(PermissionEnum.SELECT_DISCOUNT.type) == true
                    ) {
                        btnWholeDiscount.isVisible = !hasUnWeight

                        btnSingleDiscount.isVisible = !it.isHasCouponActivityGood()
                        val selectItem = orderedInfoAdapter?.getSelectItem()
                        if (selectItem != null) {
                            btnSingleDiscount.isEnabled =
                                selectItem.isHasProcessed() && selectItem.goodsType == GoodTypeEnum.NORMAL.id
                        } else {
                            btnSingleDiscount.isEnabled = false
                        }
                    }
                } else {
                    btnWholeDiscount.isVisible = false
                    btnSingleDiscount.isVisible = false
                }
            }
        }
    }

    private fun isRemarkBtnEnable(orderedInfo: OrderedInfoResponse?) {
        orderedInfo?.let {
            binding?.apply {
                if (orderedInfoAdapter?.getSelectItem() == null) {
                    btnRemark.isEnabled = true
                } else {
                    btnRemark.isEnabled =
                        orderedInfoAdapter?.getSelectItem()?.goodsType != GoodTypeEnum.TEMPORARY.id
                }
            }
        }
    }


    /**
     * 显示退菜按钮   有权限的时候才显示
     *
     */
    private fun showCancelDishBtn() {
        binding?.apply {
            if (viewModel.currentOrderedInfo?.isAfterPayStatus() == false) {
                if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                        ?.contains(PermissionEnum.CANCEL_GOOD.type) == true
                ) {
                    val selectItem = orderedInfoAdapter?.getSelectItem()
                    btnCancelDish.isVisible = viewModel.currentOrderedInfo?.isPreOrder() == false
                    btnCancelDish.isEnabled = selectItem != null
                }
            } else {
                btnCancelDish.isVisible = false
            }
        }
    }

    /**
     * 显示取消订单按钮   有权限的时候才显示
     *
     */
    private fun showCancelOrderBtn() {
        if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.CANCEL_ORDER.type) == true
        ) {
            binding?.apply {
                btnCancelOrder.isVisible = true
            }
        }
    }

    /**
     * 显示反结账按钮
     *
     * @param orderedInfo
     */
    private fun showBtnAntiSettlement(orderedInfo: OrderedInfoResponse) {
        /**
         * 反结账按钮 大于支付时间24小时就不显示
         */
        binding?.apply {
            if (MainDashboardFragment.CURRENT_USER?.getPermissionList()
                    ?.contains(PermissionEnum.REFUND.type) == true
            ) {
                val time = orderedInfo.payTime?.convertToTimestamp2() ?: 0
                val currentTime = Date().time
                btnAntiSettlement.isVisible = currentTime - time < ONE_DAY_WITH_SECOND
            } else {
                btnAntiSettlement.isVisible = false
            }

        }
    }

    /**
     * 退款按钮显示
     *
     * @param orderedInfo
     */
    private fun showBtnRefundBtn(orderedInfo: OrderedInfoResponse) {
        binding?.apply {
            btnOrderRefund.isVisible = MainDashboardFragment.CURRENT_USER?.getPermissionList()
                ?.contains(PermissionEnum.REFUND.type) == true

        }
    }

    /**
     * 更新接单UI
     *
     * @param ordered
     */
    private fun updateReceiveView(ordered: OrderedInfoResponse?) {
        binding?.apply {
            val waitAcceptInfo = ordered?.waitAcceptAddOrder
            if (waitAcceptInfo != null) {

                llWaitReceiveLayout.isVisible = true
                val adapter = OrderedInfoAdapter(arrayListOf())
                receiverInfoRecyclerView.adapter = adapter
                if (!waitAcceptInfo.goods.isNullOrEmpty()) {
                    adapter.replaceData(
                        arrayListOf(waitAcceptInfo.goods!!.first()),
                        waitAcceptInfo.refundGoodsJson,
                        waitAcceptInfo
                    )
                }

                tvViewAllReceiveInfo.isVisible = (waitAcceptInfo.goods?.size ?: 0) > 1

                tvReceiveState.text =
                    "${waitAcceptInfo.acceptStatus?.getAcceptText(requireContext())}"
                tvReceiveState.setTextColor(
                    ContextCompat.getColor(
                        requireContext(),
                        waitAcceptInfo.acceptStatus?.getAcceptTypeColor()
                            ?: R.color.ordered_cancel_color
                    )
                )
                cardAcceptStatus.setCardBackgroundColor(
                    ContextCompat.getColor(
                        requireContext(),
                        waitAcceptInfo.acceptStatus?.getAcceptTypeBackGroundColor()
                            ?: R.color.ordered_cancel_color
                    )
                )

            } else {

                llWaitReceiveLayout.isVisible = false

            }

            val cancelAcceptAddOrderList = ordered?.cancelAcceptAddOrderList

            llReceiveCancelLayout.isVisible = !cancelAcceptAddOrderList.isNullOrEmpty()
        }
    }


    /**
     * 显示称重按钮
     *
     */
    private fun isShowBtnWeigh() {
        val selectItem = orderedInfoAdapter?.getSelectItem()
        binding?.apply {
            if (viewModel.currentOrderedInfo?.needShowUnProcessState() == true && viewModel.currentOrderedInfo?.isOrderExpire() == false) {
                btnWeight.text = getString(R.string.weigh)
                if (selectItem != null) {
                    Timber.d("isToBeWeighed:${selectItem.isToBeWeighed()} isAfterPayStatus:${viewModel.currentOrderedInfo?.isAfterPayStatus()}")
                    /**
                     * 称重商品 且 未支付 显示称重按钮
                     */
                    if (selectItem.isToBeWeighed() && viewModel.currentOrderedInfo?.isAfterPayStatus() == false) {
                        btnWeight.text = if (selectItem.isHasCompleteWeight()) {
                            getString(R.string.re_weigh)
                        } else getString(R.string.weigh)
                        btnWeight.isEnabled = true
                    } else {
                        btnWeight.isEnabled = false
                    }
                } else {
                    btnWeight.isEnabled = false
                }
                Timber.d("isHasWeightGood:${viewModel.currentOrderedInfo?.isHasWeightGood()}")
                btnWeight.isVisible = viewModel.currentOrderedInfo?.isHasWeightGood() == true
            } else {
                btnWeight.isVisible = false
            }
        }
    }

    /**
     * 显示时价菜设置价格按钮
     *
     */
    private fun isShowBtnSetTimePrice() {
        val selectItem = orderedInfoAdapter?.getSelectItem()
        binding?.apply {
            if (viewModel.currentOrderedInfo?.needShowUnProcessState() == true && viewModel.currentOrderedInfo?.isOrderExpire() == false) {
                btnTimePrice.text = getString(R.string.set_time_price)
                if (selectItem != null) {
                    /**
                     * 称重商品 且 未支付 显示称重按钮
                     */
                    btnTimePrice.isEnabled =
                        selectItem.isTimePriceGood() && viewModel.currentOrderedInfo?.isAfterPayStatus() == false
                } else {
                    btnTimePrice.isEnabled = false
                }
                btnTimePrice.isVisible = viewModel.currentOrderedInfo?.isHasTimePriceGood() == true
            } else {
                btnTimePrice.isVisible = false
            }
        }
    }

    /**
     * 待定价 商品
     *
     * @param ordered
     * @return
     */
    private fun showTobeProcess(ordered: OrderedInfoResponse?): Boolean {
        //如果有称重的，且已经全部称重
        val isHasNeedProcess = ordered?.isHasNeedProcess() ?: false
        ordered?.let {
            binding?.apply {
                var unProcessNum = 0
                var unProcessServiceWhiteGoods = 0

                if (it.needShowUnProcessState()) {
                    it.goods?.forEach { good ->
                        if (!good.isHasProcessed()) {
                            unProcessNum += 1
                            if (good.serviceChargeWhitelisting == true) {
                                unProcessServiceWhiteGoods += 1
                            }
                        }
                    }
                }

                if (isHasNeedProcess) {
                    layoutOrderInfo.apply {
                        tvSubtotal.text = getString(R.string.to_be_confirmed)
                        tvTotalPrice.text = getString(R.string.to_be_confirmed)

                        tvDiscount.text = getString(R.string.to_be_confirmed)
                        tvDiscountAmount.text = getString(R.string.to_be_confirmed)

                        tvVat.text = getString(R.string.to_be_confirmed)
                        llVat.isVisible =
                            (ordered.price?.vatPercentage ?: BigDecimal(0)) > BigDecimal(0)
                        tvTotalKhrPrice.isVisible = false
                        tvVipPrice.isVisible = false

                        if (!ordered.isTakeAway()) {
                            if (unProcessNum > unProcessServiceWhiteGoods && (MainDashboardFragment.CURRENT_USER?.getCurrentServiceChargePercentage() != 0)) {
                                tvServiceFee.text = getString(R.string.to_be_confirmed)
                                llServiceFee.isVisible = true
                            }
                        }
                    }
                }
            }
        }
        return isHasNeedProcess
    }


    private fun showPopupFilterStatus(anchorView: View) {
        binding?.apply {
            activity?.hideKeyboard(edtSearch.getEditText()!!)
        }

        val popupView = PopupOrderFilterBinding.inflate(layoutInflater)
        val popupWindow = PopupWindow(
            popupView.root, anchorView.width, ViewGroup.LayoutParams.WRAP_CONTENT, true
        )
        PopupWindowHelper.addPopupWindow(popupWindow)
        popupWindow.animationStyle = R.style.PopupAnimation
        popupWindow.showAsDropDown(anchorView)
        popupWindow.setOnDismissListener {
            PopupWindowHelper.deletePopupWindow(popupWindow)
            binding?.arrowFilter?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }


        context?.let {
            when (selectedValue) {
                OrderedStatusMergeEnum.UNPAID_CONFIRM.id -> setSelectedLanguages(
                    popupView.tvUnpaid, it
                )

                OrderedStatusMergeEnum.PAID.id -> setSelectedLanguages(
                    popupView.tvPaid, it
                )

                OrderedStatusMergeEnum.All.id -> setSelectedLanguages(
                    popupView.tvAllOrder, it
                )

                OrderedStatusMergeEnum.CANCEL_ORDER.id -> setSelectedLanguages(
                    popupView.tvCancel, it
                )

                OrderedStatusMergeEnum.PREORDER.id -> setSelectedLanguages(
                    popupView.tvPreOrder, it
                )

                OrderedStatusMergeEnum.CANCEL_ORDER.id -> setSelectedLanguages(
                    popupView.tvCancel, it
                )

                OrderedStatusMergeEnum.REFUNDS.id -> setSelectedLanguages(
                    popupView.tvRefunds, it
                )
            }
        }
        popupView.tvUnpaid.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.UNPAID_CONFIRM.id
            setValueOnclickStatus(popupWindow, popupView.tvUnpaid.text.toString())
        }
        popupView.tvPaid.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.PAID.id
            setValueOnclickStatus(popupWindow, popupView.tvPaid.text.toString())
        }
        popupView.tvRefunds.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.REFUNDS.id
            setValueOnclickStatus(popupWindow, popupView.tvRefunds.text.toString())
        }
        popupView.tvAllOrder.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.All.id
            setValueOnclickStatus(popupWindow, popupView.tvAllOrder.text.toString())
        }
        popupView.tvCancel.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.CANCEL_ORDER.id
            setValueOnclickStatus(popupWindow, popupView.tvCancel.text.toString())
        }

        popupView.tvPreOrder.setOnClickListener {
            selectedValue = OrderedStatusMergeEnum.PREORDER.id
            setValueOnclickStatus(popupWindow, popupView.tvPreOrder.text.toString())
        }
    }

    private fun setValueOnclickStatus(popupWindow: PopupWindow, text: String) {
        binding?.apply {
            viewModel.getOrderedList(
                true,
                selectedValue,
                keyword = edtSearch.getSearchContent(),
                listTable = selectedTableItem,
                isFilterUnRead = isFilterUnRead,
                isFilterUnPrint = isFilterUnPrint,
                consumerId = selecteAccount?.id
            )
            tvFilter.text = text
        }

        popupWindow.dismiss()
    }

    private fun setSelectedLanguages(textView: TextView, context: Context) {
        textView.setBackgroundResource(R.drawable.background_language_selected)
        textView.setTextColor(ContextCompat.getColor(context, R.color.primaryColor))
    }

    private fun resetFilter() {
        binding?.apply {
            tvFilter.text = getString(R.string.all_order)
            tvTable.text = getString(R.string.all_table)
            tvFilterNickname.text = getString(R.string.customer_nickname)
            selectedValue = OrderedStatusEnum.All.id
            selectedTableItem.clear()
            cardViewFilterTable.isVisible = false
            isFilterUnRead = false
            isFilterUnPrint = false
            updateUnReadAndUnPrintBtn()

            selecteAccount = null
        }
    }

    private fun orderMore() {
        viewModel.currentOrderedInfo?.let {
            val reserveTableRequest = ReserveTableRequest(
                diningTime = it.getDingTime(),
                diningNumber = it.customerInfoVo?.diningNumber ?: 0,
                areaCode = it.customerInfoVo?.areaCode ?: "",
                mobile = it.customerInfoVo?.mobile ?: "",
                name = it.customerInfoVo?.name ?: "",
                tableId = ""
            )
            it.goods?.let { it1 ->
                viewModel.updateShoppingRecord(
                    reserveTableRequest, it.diningStyle ?: 0, it1
                )
            }
        }
    }

    private fun initObserverTable() {
        printerViewModel.postPrintState?.observe(viewLifecycleOwner) {
            if (it is ApiResponse.Error) {
                showToast(it.message ?: "")
            }
        }

        printerViewModel.postPreSettlementState?.observe(viewLifecycleOwner) {
            if (it is ApiResponse.Error) {
                showToast(it.message ?: "")
            }
        }

        viewModel.uiTableState.observe(viewLifecycleOwner) { it ->
            it.response?.let { it ->
                when (it) {
                    is ApiResponse.Loading -> {
                        showProgress()
                    }

                    is ApiResponse.Success -> {
                        dismissProgress()
                        popupViewTable?.apply {
                            if (it.data.isEmpty()) {
                                btnConfirm.isEnabled = false
                            }
                            val floorArray: ArrayList<String> = arrayListOf()
                            floorArray.add(getString(R.string.all))
                            it.data.sortedBy { it.id }.forEach { res ->
                                if (!floorArray.contains(res.location)) {
                                    res.location?.let { it1 -> floorArray.add(it1) }
                                }
                            }
                            floorAdapter = context?.let { it1 ->
                                FloorAdapter(floorArray, it1, 0) { floor ->
                                    viewModel.filterFloor(
                                        if (floor == 0) "" else floorArray[floor],
                                    )
                                }
                            }
                            recyclerViewPage?.adapter = floorAdapter
                        }
                    }

                    is ApiResponse.Error -> {
                        dismissProgress()
                        showToast(it.message ?: "")
                    }

                    else -> {

                    }
                }
            }

            it.orderedTableItem?.let {
                selectedTableItem = arrayListOf(it)
                binding?.apply {
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
                if (arguments?.containsKey(PAY_STATUS) == true) {
                    selectedValue = OrderedStatusMergeEnum.UNPAID_CONFIRM.id
                    binding?.tvFilter?.text = getString(R.string.unpaid)
                }
                binding?.apply {
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem,
                        isFilterUnRead = isFilterUnRead,
                        isFilterUnPrint = isFilterUnPrint,
                        consumerId = selecteAccount?.id
                    )
                }

            }

        }
        viewModel.filteredTableResponse.observe(viewLifecycleOwner) {
            filterTableAdapter?.updateItems(it)
            popupViewTable?.btnConfirm?.text =
                getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
        }
    }

    private var tablePopupWindow: PopupWindow? = null

    private fun initPopuptableView() {
        initObserverTable()
        popupViewTable = DialogFilterTableListBinding.inflate(layoutInflater)
        tablePopupWindow = PopupWindow(
            popupViewTable?.root,
            (Resources.getSystem().displayMetrics.widthPixels / 2.2).toInt(),
            (Resources.getSystem().displayMetrics.heightPixels / 1.5).toInt(),
            true
        )
        tablePopupWindow?.elevation = 20f
        tablePopupWindow?.animationStyle = R.style.PopupAnimation
        viewModel.getOrderedTableList(tableUUID = arguments?.getString(TABLE_UUID))
        var tableList = OrderedTableListResponse()
        popupViewTable?.apply {
            filterTableAdapter = context?.let {
                FilterTableAdapter(
                    tableList, it, selectedTableItem
                ) {
                    btnConfirm.text =
                        getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
                }
            }
            recyclerViewTable.adapter = filterTableAdapter
            btnConfirm.text = getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
            btnConfirm.setOnClickListener {
                selectedTableItem = ArrayList(filterTableAdapter?.getSelectedArrayList())
                binding?.apply {
                    cardViewFilterTable.setVisibleGone(selectedTableItem.size > 1)
                    if (selectedTableItem.isEmpty()) {
                        tvTable.text = getString(R.string.all_table)
                    } else {
                        tvTable.text = selectedTableItem.first().name
                        tvCount.text = "+${selectedTableItem.size - 1}"
                    }
                }
                binding?.apply {
                    viewModel.getOrderedList(
                        true,
                        selectedValue,
                        keyword = edtSearch.getSearchContent(),
                        listTable = selectedTableItem,
                        isFilterUnRead = isFilterUnRead,
                        isFilterUnPrint = isFilterUnPrint,
                        consumerId = selecteAccount?.id
                    )
                }

                tablePopupWindow?.dismiss()
            }

            btnCancel.setOnClickListener {
                filterTableAdapter?.resetSelect()
                btnConfirm.text =
                    getString(R.string.apply_filter).plus(" (${filterTableAdapter?.getSelectedArrayList()?.size})")
            }

        }
    }

    private fun showPopupTableView(anchorView: View) {
        binding?.apply {
            activity?.hideKeyboard(edtSearch.getEditText()!!)
        }
        filterTableAdapter?.updateItemCheck(selectedTableItem)
        popupViewTable?.apply {
            btnConfirm.text = getString(R.string.apply_filter).plus(" (${selectedTableItem.size})")
            // 获取锚点视图的位置信息
            val anchorLocation = IntArray(2)
            anchorView.getLocationOnScreen(anchorLocation)
            val anchorWidth = anchorView.width
            // 计算 PopupWindow 的显示位置偏移量
            tablePopupWindow?.contentView?.measure(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT
            )
        }
        tablePopupWindow?.showAsDropDown(anchorView)

        tablePopupWindow?.setOnDismissListener {
            binding?.arrowTable?.animate()?.rotation(0f)?.setDuration(200)
            anchorView.setBackgroundResource(R.drawable.background_language_spiner)
        }

    }

    override fun onResume() {
        NetworkHelper.setWsMessageListener(object : NetworkHelper.onWsMessageListener {
            override fun onMessage(event: WebSocket.Event) {
                wsHandel(event)
            }
        })
        startCountDown()
        super.onResume()
    }


    private fun wsHandel(event: WebSocket.Event) {
        when (event) {
            is WebSocket.Event.OnConnectionOpened<*> -> Timber.e("connection opened")
            is WebSocket.Event.OnConnectionClosed -> Timber.e("connection closed")
            is WebSocket.Event.OnConnectionClosing -> Timber.e(
                "closing connection.."
            )

            is WebSocket.Event.OnConnectionFailed -> Timber.e("connection failed")
            is WebSocket.Event.OnMessageReceived -> {
                if (event.message is Message.Text) {
                    if ((event.message as Message.Text).value == "ping")
//                        viewModel.testingWebsocketSendMessage()
                    else {
                        try {
                            Timber.e("ws ==> ${(event.message as Message.Text).value}")
                            val socketModel = JSON.parseObject(
                                (event.message as Message.Text).value, SocketModel::class.java
                            )
                            socketModel.cmd?.let { cmd ->
                                when (cmd) {
                                    WsCommand.REPAYMENT -> {
                                        viewModel.getOrderedList(
                                            true,
                                            selectedValue,
                                            keyword = binding?.edtSearch?.getSearchContent(),
                                            listTable = selectedTableItem,
                                            isFilterUnRead = isFilterUnRead,
                                            isFilterUnPrint = isFilterUnPrint,
                                            consumerId = selecteAccount?.id
                                        )
                                    }

                                    WsCommand.RESERVE_ORDER_CANCEL, WsCommand.ANTI_SETTLEMENT -> {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = JSON.parseObject(
                                                    it.toJson(), WsReserveOrderCancel::class.java
                                                )
                                                Timber.e(" data.orderNo-> ${data.orderNo}")
                                                if (data.orderNo != null) {
                                                    orderedAdapter?.hasModel(data.orderNo)?.run {
                                                        viewModel.websocketRefreshOrder(this)
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    WsCommand.ACTIVITY_COUPON_CHANGE, WsCommand.CONFIRMED_TO_UNPAID_STATUS_ALTER, WsCommand.ORDER_HAS_READ, WsCommand.QRCODE_PAY_SUCCEED -> {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                orderedAdapter?.hasModel(it.toString())?.run {
                                                    //延迟500调用 防止服务器信息还没变更
                                                    viewModel.websocketRefreshOrder(this)
                                                }
                                                //这里不能关闭支付弹窗 不然 会有问题
                                            }
                                        }
                                    }

                                    WsCommand.CANCEL_CREDIT, WsCommand.ORDER_CREDIT, WsCommand.OFFLINE_PAY_SUCCEED, WsCommand.CONFIRMED_GOODS_INFO, WsCommand.CONVENTION_ORDER_CANCEL, WsCommand.BALANCE_PAY_SUCCEED, WsCommand.USER_CANCEL_ORDER, WsCommand.ORDER_STATUS_BE_CONFIRM, 27, 28, WsCommand.GOODS_REDUCE_DISCOUNT_CHANGE, WsCommand.CHANGE_ORDER_REMARK -> {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                orderedAdapter?.hasModel(it.toString())?.run {
                                                    //延迟500调用 防止服务器信息还没变更
                                                    viewModel.websocketRefreshOrder(this)
                                                    if (this.orderNo == viewModel.currentOrderedInfo?.orderNo) {
//                                                        PayDialog.dismissDialog(
//                                                            parentFragmentManager
//                                                        )
//                                                        MixedPayDialog.dismissDialog(
//                                                            parentFragmentManager
//                                                        )
                                                        CreditDialog.dismissDialog(
                                                            parentFragmentManager
                                                        )
                                                        dismissProgress()
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    WsCommand.GOODS_CHANGE_WEIGHT -> {
                                        //在员工端称重同步
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(), WsModifyWeight::class.java
                                            )
                                            if (data.orderId != null) {
                                                orderedAdapter?.hasModel(data.orderId)?.run {
                                                    viewModel.websocketRefreshOrder(this)
                                                }
                                            }
                                        }
                                    }

                                    25 -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(), WsRefundModel::class.java
                                            )
                                            if (data.orderNo != null) {
                                                orderedAdapter?.hasModel(data.orderNo)?.run {
                                                    viewModel.websocketRefreshOrder(this)
                                                }
                                            }
                                        }
                                    }

                                    /**
                                     * 创建新订单/支付订单
                                     */
                                    WsCommand.CREATE_ORDER, WsCommand.PAY_ORDER -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(), OrderedInfoResponse::class.java
                                            )

                                            data?.apply {
                                                if (orderNo != null) {
                                                    binding?.apply {
                                                        //没有过滤的时候才插入
                                                        if (selectedValue == OrderedStatusEnum.All.id && selectedTableItem.isEmpty() && edtSearch.getSearchContent()
                                                                .isNullOrEmpty()
                                                        ) {
                                                            //用户端下单 下发订单信息 插入列表
                                                            if (orderedAdapter?.hasModel(orderNo) == null) {
                                                                val isAtTop =
                                                                    !orderListRecyclerView.canScrollVertically(
                                                                        -1
                                                                    )
                                                                binding?.apply {
                                                                    orderedAdapter?.insetOrderToTop(
                                                                        toOrderedRecord()
                                                                    )
                                                                    //判断一下列表是否在顶部，在顶部就滚动到最上面
                                                                    if (isAtTop) {
                                                                        orderListRecyclerView?.postDelayed(
                                                                            {
                                                                                orderListRecyclerView.smoothScrollToPosition(
                                                                                    0
                                                                                )
                                                                            }, 200
                                                                        )

                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                    closeOrderAmountDetailDialog(orderNo)
                                                }
                                            }

                                        }
                                    }
                                    /**
                                     * 新的待接单
                                     */
                                    WsCommand.NEW_ACCEPT_ORDER -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(), OrderedInfoResponse::class.java
                                            )

                                            data?.apply {
                                                if (viewModel.currentOrderedInfo?.orderNo == orderNo) {
                                                    Timber.e("当前订单有新的接单")
                                                    viewModel.getOrderedInfo(data.orderNo!!)
                                                } else {
                                                    orderedAdapter?.list?.forEachIndexed { index, orderedRecord ->
                                                        if (orderedRecord.orderNo == orderNo) {
                                                            orderedRecord.expireCountDown =
                                                                expireCountDown
                                                            requireActivity().runOnUiThread {
                                                                orderedAdapter?.notifyItemChanged(
                                                                    index, COUNT_DOWN
                                                                )
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                        }
                                    }

                                    /**
                                     * 取消接单,待接单 有更新 , 接单
                                     */
                                    WsCommand.CANCEL_ACCEPT_ORDER, WsCommand.ACCEPT_ORDER_UPDATE, WsCommand.ACCEPT_ORDER -> {
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = Gson().fromJson(
                                                    it.toJson(),
                                                    WsAcceptOrderChangeResponse::class.java
                                                )

                                                orderedAdapter?.hasModel(data.orderNo.toString())
                                                    ?.run {
                                                        //延迟500调用 防止服务器信息还没变更
                                                        viewModel.getOrderedInfo(data.orderNo!!)
                                                    }

                                                closeWaitReceiveOrderGoodsListDialog(data.orderNo)
                                            }

                                        }
                                    }

                                    WsCommand.MERGE_ORDER -> {
                                        /**
                                         * 合并订单
                                         */
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = Gson().fromJson(
                                                    it.toJson(), WsMergeOrderResponse::class.java
                                                )

                                                if (viewModel.currentOrderedInfo?.orderNo == data.orderId || data.subOrders?.contains(
                                                        viewModel.currentOrderedInfo?.orderNo
                                                    ) == true
                                                ) {
                                                    //如果当前订单是主订单 或者 当前订单是合单的子单  ,刷新列表 并且定位到这个订单主单
                                                    viewModel.getOrderedList(
                                                        isRefresh = true,
                                                        selectedValue,
                                                        keyword = edtSearch.getSearchContent(),
                                                        listTable = selectedTableItem,
                                                        localOrderNo = data.orderId,
                                                        isFilterUnRead = isFilterUnRead,
                                                        isFilterUnPrint = isFilterUnPrint,
                                                        consumerId = selecteAccount?.id
                                                    )
                                                    closeWaitReceiveOrderGoodsListDialog(viewModel.currentOrderedInfo?.orderNo)
                                                } else {
                                                    //如果
                                                    viewModel.getOrderedList(
                                                        isRefresh = true,
                                                        selectedValue,
                                                        keyword = edtSearch.getSearchContent(),
                                                        listTable = selectedTableItem,
                                                        localOrderNo = viewModel.currentOrderedInfo?.orderNo,
                                                        isFilterUnRead = isFilterUnRead,
                                                        isFilterUnPrint = isFilterUnPrint,
                                                        consumerId = selecteAccount?.id
                                                    )
                                                }
                                            }
                                        }
                                    }

                                    WsCommand.SPLIT_ORDER -> {
                                        /**
                                         * 拆分订单
                                         */
                                        binding?.apply {
                                            socketModel.data?.let {
                                                val data = Gson().fromJson(
                                                    it.toJson(), WsMergeOrderResponse::class.java
                                                )
                                                viewModel.getOrderedList(
                                                    isRefresh = true,
                                                    selectedValue,
                                                    keyword = edtSearch.getSearchContent(),
                                                    listTable = selectedTableItem,
                                                    localOrderNo = viewModel.currentOrderedInfo?.orderNo,
                                                    isFilterUnRead = isFilterUnRead,
                                                    isFilterUnPrint = isFilterUnPrint,
                                                    consumerId = selecteAccount?.id
                                                )
                                            }
                                        }
                                    }

                                    WsCommand.PRINT_PRE_SETTLEMENT_STATUS_CHANGE -> {
                                        socketModel.data?.let {
                                            val data = Gson().fromJson(
                                                it.toJson(),
                                                WsOrderPreSettlementChangeResponse::class.java
                                            )
                                            if (data.orderNo != null) {
                                                orderedAdapter?.hasModel(data.orderNo)?.run {
//                                                    viewModel.websocketRefreshOrder(this)
                                                    viewModel.updateOrderRecord(this, data)
                                                }
                                            }

//                                            viewModel.getOrderedList(
//                                                isRefresh = true,
//                                                selectedValue,
//                                                keyword = edtSearch.getSearchContent(),
//                                                listTable = selectedTableItem,
//                                                localOrderNo = viewModel.currentOrderedInfo?.orderNo,
//                                                isFilterUnRead = isFilterUnRead,
//                                                isFilterUnPrint = isFilterUnPrint
//                                            )
                                        }
                                    }

                                    WsCommand.CUSTOM_INFO_CHANGE -> {
                                        socketModel.data?.let { it ->
                                            val updateCustomerInfo = Gson().fromJson(
                                                it.toJson(), UpdateCustomerInfoRequest::class.java
                                            )
                                            updateCustomerInfo.orderId?.let { orderNo ->
                                                if (viewModel.currentOrderedInfo?.orderNo == orderNo) {
                                                    viewModel.getOrderedInfo(orderNo)
                                                }
                                            }
                                        }
                                    }
//                                    WsCommand.CUSTOM_INFO_CHANGE -> {
//                                        socketModel.data?.let {
//                                            val updateCustomerInfo = Gson().fromJson(
//                                                it.toJson(),
//                                                UpdateCustomerInfoRequest::class.java
//                                            )
//                                            viewModel.updateCustomerFromWs(updateCustomerInfo)
//                                        }
//                                    }

                                    else -> {}
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
            }
        }
    }

    private fun updateUnRead() {
        binding?.apply {
            tvUnRead.text = UnReadAndUnPrintHelper.getUnReadNumStr()
            tvUnPrint.text = UnReadAndUnPrintHelper.getUnPrintNumStr()
        }
    }


    private fun initEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }


    /**
     * 当前订单取消后的一些处理
     *
     * @param orderNo
     */
    private fun handlerAfterOrderCancel(orderNo: String?) {
        /**
         * 关闭修改折扣弹窗
         */
        val currentDiscountOrderNo = NewModifyDiscountDialog.getCurrentModifyDiscountDialog(
            parentFragmentManager
        )?.getCurrentOrderNo()
        if (currentDiscountOrderNo == orderNo.toString()) {
            NewModifyDiscountDialog.dismissDialog(
                parentFragmentManager
            )
        }

        /**
         * 关闭取消订单弹窗
         */
        val currentCancelOrderNo = CancelOrderDialog.getCurrentCancelOrderDialog(
            parentFragmentManager
        )?.getCurrentOrderNo()

        if (currentCancelOrderNo == orderNo.toString()) {
            CancelOrderDialog.dismissDialog(
                parentFragmentManager
            )
        }

        /**
         * 关闭取消挂账弹窗
         */
        val currentCancelCreditNo = CancelCreditDialog.getCurrentCancelOrderDialog(
            parentFragmentManager
        )?.getCurrentOrderNo()

        if (currentCancelCreditNo == orderNo.toString()) {
            CancelCreditDialog.dismissDialog(
                parentFragmentManager
            )
        }

        closeWaitReceiveOrderGoodsListDialog(orderNo)
    }

    private fun closeOrderAmountDetailDialog(orderNo: String?) {
        val dialogOrderId = OrderAmountDetailDialog.getCurrentOrderAmountDetailDialog(
            parentFragmentManager
        )?.getCurrentOrderNo()
        if (dialogOrderId == orderNo.toString()) {
            OrderAmountDetailDialog.dismissDialog(
                parentFragmentManager
            )
        }
    }

    /**
     * 关闭加购 接单详情弹窗
     */
    private fun closeWaitReceiveOrderGoodsListDialog(orderNo: String?) {
        val currentWaitReceiveOrderGoodsListNo =
            WaitReceiveOrderGoodsListDialog.getCurrentWaitReceiveOrderGoodsListDialog(
                parentFragmentManager
            )?.getCurrentOrderNo()
        if (currentWaitReceiveOrderGoodsListNo == orderNo.toString()) {
            WaitReceiveOrderGoodsListDialog.dismissDialog(
                parentFragmentManager
            )
        }
    }

    /**
     * 显示支付弹窗
     *
     * @param anchorView
     */
//    private fun showPopupWindowPaymentMethod(anchorView: View) {
//        //防止帕金森，这样就没意思了
//        if ((activity?.supportFragmentManager ?: parentFragmentManager).findFragmentByTag(
//                PayByBalanceDialog.PAY_BY_BALANCE
//            ) != null
//        ) {
//            Timber.e("余额弹窗已经点开")
//            return
//        }
//
//        binding?.apply {
//            activity?.hideKeyboard(edtSearch.getEditText()!!)
//        }
//        val popupView = PopupPaynowBinding.inflate(layoutInflater)
//        val popupWindow = PopupWindow(
//            popupView.root,
//            anchorView.width,
//            ViewGroup.LayoutParams.WRAP_CONTENT,
//            true
//        )
//        PopupWindowHelper.addPopupWindow(popupWindow)
//        popupWindow.animationStyle = R.style.PopupAnimation
//        popupWindow.setOnDismissListener {
//            PopupWindowHelper.deletePopupWindow(popupWindow)
//        }
//        val location = IntArray(2).apply {
//            anchorView.getLocationOnScreen(this)
//        }
//        popupWindow.contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
//
//        popupWindow.showAtLocation(
//            anchorView,
//            Gravity.BOTTOM or Gravity.START,
//            location[0],
//            100
//        )
//
//        if (viewModel.currentOrderedInfo?.sourcePlatform == SourcePlatformEnum.Kiosk.id) {
//            /**
//             * kiosk 只支持线下支付
//             */
//            popupView.tvBalance.isVisible = false
//            popupView.tvOnlinePayment.isVisible = false
//        }
//
//        popupView.tvOnlinePayment.setOnClickListener {
//            SingleClickUtils.isFastDoubleClick {
//                viewModel.payAgain(PayTypeEnum.ONLINE_PAYMENT)
//                popupWindow.dismiss()
//            }
//        }
//
//        popupView.tvPayByCash.setOnClickListener {
//            if (viewModel.currentOrderedInfo?.getRealPayPrice() == 0L) {
//                //0圆够，线下 直接传现金支付
//                Printer.openCashierBox()
//
//                viewModel.payAgain(
//                    PayTypeEnum.CASH_PAYMENT,
//                    offlineChannelModel = PaymentMethodHelper.getCashPaymentModel(),
//                    cashConvert = CashConvertModel(0, 0, 0, 0.0)
//                )
//
//            } else {
//                OfflineChannelsDialog.showDialog(
//                    parentFragmentManager,
//                ) {
//                    if (it.id == OfflinePaymentChannelEnum.CASH.id) {
//                        Printer.openCashierBox()
//                        cashPopDialog?.dismiss()
//                        cashPopDialog = CashConvertDialog(requireContext()).showDialog(
//                            viewModel.currentOrderedInfo?.getRealPayPrice()!!,
//                            it,
//                            menuOrderScreen = orderedScreen,
//                            {
//                                cashPopDialog = null
//                            }
//                        ) { cashConvert ->
//                            viewModel.payAgain(
//                                PayTypeEnum.CASH_PAYMENT,
//                                offlineChannelModel = it,
//                                cashConvert = cashConvert
//                            )
//                        }
//                    } else {
//                        viewModel.payAgain(
//                            PayTypeEnum.CASH_PAYMENT,
//                            offlineChannelModel = it
//                        )
//                    }
//                }
//            }
//            popupWindow.dismiss()
//        }
//
//        popupView.tvBalance.setOnClickListener {
//            var price =
//                viewModel.currentOrderedInfo?.getRealPayPrice()
//            if (viewModel.currentOrderedInfo?.isShowVipPrice() == true) {
//                price = totalVipPrice.times(BigDecimal(100)).toLong()
//            }
//
//            PayByBalanceDialog.showDialog(
//                fragmentManager = activity?.supportFragmentManager
//                    ?: parentFragmentManager,
//                content = price,
//                countryCode = viewModel.currentOrderedInfo?.customerInfoVo?.areaCode,
//                phone = viewModel.currentOrderedInfo?.customerInfoVo?.mobile,
//                callBackClickListener = {
//                    ConfirmDialog.showDialog(
//                        parentFragmentManager,
//                        content = getString(R.string.insufficient_balance),
//                        positiveButtonTitle = getString(R.string.top_up),
//                        negativeButtonTitle = getString(R.string.change_payment),
//                    ) {
//                        TopupBalanceDialog.showDialog(
//                            parentFragmentManager,
//                            content = it.toJson()
//                        ) { response ->
//                            if (response.data != null) {
//                                TopupQrDialog.showDialog(
//                                    fragmentManager = activity?.supportFragmentManager
//                                        ?: parentFragmentManager,
//                                    paymentResponse = response.data,
//                                    getOrderedScreen(),
//                                    onCloseListener = {
//                                        getOrderedScreen()?.showOrder()
//                                    },
//                                    successListener = {
//                                        TopUpSuccessDialog.showDialog(
//                                            parentFragmentManager
//                                        ) {
//                                            getOrderedScreen()?.showOrder()
//                                        }
//                                    })
//                            } else {
//                                TopUpSuccessDialog.showDialog(
//                                    parentFragmentManager
//                                )
//                            }
//                        }
//                    }
//                },
//                toPayClick = {
//                    viewModel.payAgain(
//                        PayTypeEnum.USER_BALANCE,
//                        accountId = it
//                    )
//                })
//
//            popupWindow.dismiss()
//
//        }
//    }
//

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onSimpleEventASYNC(event: SimpleEvent<Any>) {
//        Timber.e("onSimpleEventASYNC  => ${event.eventType}")
        when (event.eventType) {


            SimpleEventType.UPDATE_UNREAD_EVENT -> {
                requireActivity().runOnUiThread {
                    updateUnRead()
                }
            }

            SimpleEventType.UPDATE_UNPRINT_EVENT -> {
                requireActivity().runOnUiThread {
                    orderedAdapter?.updatePrintStatus(event.data.toString())
                }

            }

            SimpleEventType.UPDATE_ORDER_LIST -> {
                requireActivity().runOnUiThread {
                    binding?.apply {
                        viewModel.getOrderedList(
                            isRefresh = true,
                            selectedValue,
                            keyword = edtSearch.getSearchContent(),
                            listTable = selectedTableItem,
                            localOrderNo = viewModel.currentOrderedInfo?.orderNo,
                            isFilterUnRead = isFilterUnRead,
                            isFilterUnPrint = isFilterUnPrint,
                            consumerId = selecteAccount?.id
                        )
                    }
                }
            }

            else -> {

            }

        }
    }


    private var timer: Timer? = null

    /**
     * 开始倒计时
     *
     */
    private fun startCountDown() {
        stopCountDown()
        Timber.e("开始倒计时")
        val task = object : TimerTask() {
            override fun run() {
                /**
                 * 刷新列表上面的倒计时
                 */
                //是否有过期订单
                var isHasExpiredOrder = false
                requireActivity().runOnUiThread {
                    orderedAdapter?.list?.forEachIndexed { index, orderedRecord ->
                        orderedRecord.expireCountDown = (orderedRecord.expireCountDown ?: 0) - 1000

                        if ((orderedRecord.expireCountDown ?: 0) < 0) {
                            orderedRecord.expireCountDown = 0
                        }
                        if (orderedRecord.expireCountDown != 0L) {
                            orderedAdapter?.notifyItemChanged(index, OrderedAdapter.COUNT_DOWN)
                        }
                        if ((orderedRecord.acceptStatus == AcceptOrderedStatusEnum.WAIT_ACCEPT.id) && ((orderedRecord.expireCountDown
                                ?: 0) <= 0L)
                        ) {
                            //如果待接单 且倒计时到0
                            orderedRecord.acceptStatus = AcceptOrderedStatusEnum.ACCEPTED.id
                            orderedAdapter?.notifyItemChanged(index, OrderedAdapter.COUNT_DOWN)
                        }


                        /**
                         * 把当前订单页刷新一下
                         */
                        binding?.apply {
                            layoutDetailInfo.apply {
                                if (viewModel.currentOrderedInfo?.orderNo == orderedRecord.orderNo) {
//                                    Timber.e("viewModel.currentOrderedInfo?.orderNo :${viewModel.currentOrderedInfo?.orderNo}  orderNo:${orderedRecord.orderNo}")
                                    if (orderedRecord.isCanReceiveOrder()) {
                                        btnAcceptOrder.text =
                                            getString(R.string.receiving_orders).plus(
                                                " ${
                                                    TimeUtils.convertMillisecondsToTime(
                                                        orderedRecord.expireCountDown ?: 0L
                                                    )
                                                }"
                                            )
                                        try {
                                            if (isAdded) {
                                                val fragmentDialog =
                                                    WaitReceiveOrderGoodsListDialog.getCurrentWaitReceiveOrderGoodsListDialog(
                                                        parentFragmentManager
                                                    )
                                                if (fragmentDialog != null) {
                                                    val orderNo = fragmentDialog.getCurrentOrderNo()
                                                    if (orderNo == viewModel.currentOrderedInfo?.orderNo) {
                                                        fragmentDialog.updateCountDown(
                                                            btnAcceptOrder.text.toString()
                                                        )
                                                    }
                                                }
                                            }
                                        } catch (e: Exception) {

                                        }

                                    } else {
                                        llWaitReceiveLayout.isVisible = false

                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        timer = Timer()
        timer?.schedule(task, 0, 1000)
    }

    /**
     * 停止倒计时
     *
     */
    private fun stopCountDown() {
        Timber.e("停止倒计时")
        if (timer != null) {
            timer?.cancel()
            timer = null
        }
    }

    private fun checkPermissions(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            val readPermission = ContextCompat.checkSelfPermission(
                requireActivity(), Manifest.permission.READ_EXTERNAL_STORAGE
            )
            val writePermission = ContextCompat.checkSelfPermission(
                requireActivity(), Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            readPermission == PackageManager.PERMISSION_GRANTED && writePermission == PackageManager.PERMISSION_GRANTED
        }
    }


    private val PERMISSION_REQUEST_CODE = 100
    private fun requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                val intent = Intent(
                    android.provider.Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION
                )
                startActivity(intent)
            } catch (e: Exception) {
                val intent = Intent()
                intent.action =
                    android.provider.Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION
                intent.addCategory("android.intent.category.DEFAULT")
                intent.data = android.net.Uri.parse(
                    String.format(
                        "package:%s", requireActivity().packageName
                    )
                )
                startActivity(intent)
            }
        } else {
            requestPermissions(
                arrayOf(
                    Manifest.permission.READ_EXTERNAL_STORAGE,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ), PERMISSION_REQUEST_CODE
            )
//            ActivityCompat.requestPermissions(
//                requireActivity(),
//                arrayOf(
//                    Manifest.permission.READ_EXTERNAL_STORAGE,
//                    Manifest.permission.WRITE_EXTERNAL_STORAGE
//                ),
//                PERMISSION_REQUEST_CODE
//            )
        }
    }


    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<out String>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                exportAgain()
            } else {
                Timber.e("权限被拒绝，无法下载文件")
//                Toast.e(this, "权限被拒绝，无法下载文件", Toast.LENGTH_SHORT).show()
            }
        }
    }

}