package com.metathought.food_order.casheir.ui.dialog

import android.content.Context
import android.hardware.display.DisplayManager
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Display
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.response_model.order.Goods
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderedInfoResponse
import com.metathought.food_order.casheir.databinding.DialogServiceFeeDetailBinding
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.ui.adapter.ServiceFeeDetailItemAdapter
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class ServiceFeeDetailDialog : BaseDialogFragment() {
    private var binding: DialogServiceFeeDetailBinding? = null
    private var goods: List<Goods?>? = null
    private var orderedInfo: OrderedInfoResponse? = null
    private var adapter: ServiceFeeDetailItemAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogServiceFeeDetailBinding.inflate(layoutInflater)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dialog?.setCancelable(true)
        initData()
        initListener()
        initObserver()
    }


    override fun onResume() {
        super.onResume()
        context?.let {
            val displayMetrics = getDisplayMetrics(it)
            val screenHeight = (displayMetrics.heightPixels * 0.6).toInt()
            val screenWidth = (displayMetrics.widthPixels * 0.5).toInt()
            dialog?.window?.setLayout(screenWidth, screenHeight)
        }
    }

    private fun initObserver() {

    }


    private fun initData() {
        binding?.apply {
            context?.let {
                Timber.e("goods => ${goods?.size}")
                goods?.let { it1 ->
                    //过滤出非白名单的
                    val filterList =
                        it1.filter { (it?.serviceChargeWhitelisting != true && ((it?.totalServiceCharge != 0L || it?.totalVipServiceCharge != 0L || it?.totalDiscountServiceCharge != 0L) || !it.isHasProcessed())) }

                    adapter = ServiceFeeDetailItemAdapter(ArrayList(filterList), orderedInfo)
                    recyclerOrderedFood.adapter = adapter

                    countTotalPrice()

                }

            }
        }
    }

    private fun countTotalPrice() {
        var totalPrice = 0L
        var totalVipPrice = 0L
        var isShowVip = false
        var isNeedProcess = false
        goods?.forEach {
            if (orderedInfo == null || orderedInfo?.isAfterPayStatus() == false) {
                Timber.e(" ${it?.serviceChargeWhitelisting} it.name:${it?.name}")
                if (it?.serviceChargeWhitelisting != true) {
                    //过滤白名单
                    if (it?.isShowVipPrice() == true) {
                        isShowVip = true
                        if (orderedInfo?.isOrderExpire() == true) {
                            isShowVip = false
                        }
                    }
                    if (it?.isHasProcessed() == false) {
                        isNeedProcess = true
                    } else {
                        totalPrice += (it?.totalDiscountServiceCharge ?: 0)
                        totalVipPrice += (it?.totalVipServiceCharge ?: 0)
                    }
                }
            } else {
                //支付后的显示计算实际支付的
                totalPrice = orderedInfo?.getRealServiceFeePrice() ?: 0
            }
        }


        binding?.apply {
            if (isNeedProcess) {
                tvServiceFee.text = getString(R.string.to_be_confirmed)
                tvVipServiceFee.isVisible = false
            } else {
                tvServiceFee.text = "${totalPrice.priceFormatTwoDigitZero2()}"
                tvVipServiceFee.text = "${totalVipPrice.priceFormatTwoDigitZero2()}"
                tvVipServiceFee.isVisible = isShowVip
            }

        }
    }


    private fun initListener() {
        binding?.apply {
            btnClose.setOnClickListener() {
                dismissAllowingStateLoss()
            }

        }
    }

    companion object {
        private const val TAG = "ServiceFeeDetailDialog"

        fun showDialog(
            fragmentManager: FragmentManager,
            goods: List<Goods?>?,
            orderedInfo: OrderedInfoResponse? = null
        ) {
            var fragment = fragmentManager.findFragmentByTag(TAG)
            if (fragment != null) return

            fragment = newInstance(
                goods = goods,
                orderedInfo = orderedInfo
            )
            fragment.show(fragmentManager, TAG)
        }

        fun dismissDialog(fragmentManager: FragmentManager) {
            val fragment =
                fragmentManager.findFragmentByTag(TAG) as? ServiceFeeDetailDialog
            fragment?.dismissAllowingStateLoss()
        }

        private fun newInstance(
            goods: List<Goods?>?, orderedInfo: OrderedInfoResponse? = null
        ): ServiceFeeDetailDialog {
            val args = Bundle()
            val fragment = ServiceFeeDetailDialog()
            fragment.goods = goods
            fragment.orderedInfo = orderedInfo
            fragment.arguments = args
            return fragment
        }
    }
}
