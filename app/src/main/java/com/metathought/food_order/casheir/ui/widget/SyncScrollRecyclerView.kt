package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.widget.HorizontalScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * 自定义RecyclerView，支持与HorizontalScrollView同步滑动
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class SyncScrollRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private var headerScrollView: HorizontalScrollView? = null
    private var isScrollingFromHeader = false
    private var isScrollingFromRecycler = false
    private var currentScrollX = 0 // 保存当前的水平滑动位置
    private val syncHandler = Handler(Looper.getMainLooper())
    private var syncRunnable: Runnable? = null

    /**
     * 设置需要同步滑动的表头ScrollView
     */
    fun setHeaderScrollView(headerScrollView: HorizontalScrollView) {
        this.headerScrollView = headerScrollView
        setupScrollSync()
    }

    private fun setupScrollSync() {
        headerScrollView?.setOnScrollChangeListener { _, scrollX, _, _, _ ->
            if (!isScrollingFromRecycler) {
                isScrollingFromHeader = true
                currentScrollX = scrollX // 保存当前滑动位置
                // 同步RecyclerView中每个item的滑动位置
                syncRecyclerViewScroll(scrollX)
                isScrollingFromHeader = false
            }
        }
    }

    private fun syncRecyclerViewScroll(scrollX: Int) {
        // 遍历RecyclerView中可见的ViewHolder
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            // 查找每个item中的HorizontalScrollView
            val itemScrollView = findHorizontalScrollView(child)
            itemScrollView?.scrollTo(scrollX, 0)
        }
    }

    private fun findHorizontalScrollView(view: android.view.View): HorizontalScrollView? {
        if (view is HorizontalScrollView) {
            return view
        }
        if (view is android.view.ViewGroup) {
            for (i in 0 until view.childCount) {
                val found = findHorizontalScrollView(view.getChildAt(i))
                if (found != null) return found
            }
        }
        return null
    }

    override fun onScrolled(dx: Int, dy: Int) {
        super.onScrolled(dx, dy)

        // 当RecyclerView垂直滚动时，延迟同步以确保新出现的item保持当前的水平滑动位置
        if (!isScrollingFromHeader && dy != 0) {
            scheduleSync()
        }
    }

    override fun onChildAttachedToWindow(child: android.view.View) {
        super.onChildAttachedToWindow(child)
        // 当新的item添加到RecyclerView时，设置其滑动监听并同步位置
        val itemScrollView = findHorizontalScrollView(child)
        itemScrollView?.let { scrollView ->
            setupItemScrollListener(scrollView)
            // 使用保存的滑动位置来设置新item的位置
            scrollView.scrollTo(currentScrollX, 0)
        }
    }

    private fun setupItemScrollListener(itemScrollView: HorizontalScrollView) {
        // 优化滑动体验，减少阻尼感
        itemScrollView.apply {
            overScrollMode = android.view.View.OVER_SCROLL_NEVER
            isHorizontalFadingEdgeEnabled = false
            isVerticalFadingEdgeEnabled = false
        }

        // 设置滑动监听，确保同步
        itemScrollView.setOnScrollChangeListener { _, scrollX, _, _, _ ->
            if (!isScrollingFromHeader) {
                isScrollingFromRecycler = true
                currentScrollX = scrollX // 保存当前滑动位置
                // 同步表头滑动
                headerScrollView?.scrollTo(scrollX, 0)
                // 同步其他item的滑动
                syncRecyclerViewScroll(scrollX)
                isScrollingFromRecycler = false
            }
        }
    }

    /**
     * 延迟同步，避免频繁调用
     */
    private fun scheduleSync() {
        syncRunnable?.let { syncHandler.removeCallbacks(it) }
        syncRunnable = Runnable {
            syncRecyclerViewScroll(currentScrollX)
        }
        syncHandler.postDelayed(syncRunnable!!, 50) // 延迟50ms执行
    }

    /**
     * 强制同步所有可见item到当前滑动位置
     */
    private fun forceSync() {
        post {
            syncRecyclerViewScroll(currentScrollX)
        }
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        // 布局完成后强制同步一次，确保所有item都在正确位置
        if (changed) {
            forceSync()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        // 清理Handler回调
        syncRunnable?.let { syncHandler.removeCallbacks(it) }
    }
}
