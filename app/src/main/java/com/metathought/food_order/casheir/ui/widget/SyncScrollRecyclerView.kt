package com.metathought.food_order.casheir.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.widget.HorizontalScrollView
import androidx.recyclerview.widget.RecyclerView

/**
 * 自定义RecyclerView，支持与HorizontalScrollView同步滑动
 * <AUTHOR> Assistant
 * @date 2025/01/26
 */
class SyncScrollRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private var headerScrollView: HorizontalScrollView? = null
    private var isScrollingFromHeader = false
    private var isScrollingFromRecycler = false

    /**
     * 设置需要同步滑动的表头ScrollView
     */
    fun setHeaderScrollView(headerScrollView: HorizontalScrollView) {
        this.headerScrollView = headerScrollView
        setupScrollSync()
    }

    private fun setupScrollSync() {
        headerScrollView?.setOnScrollChangeListener { _, scrollX, _, _, _ ->
            if (!isScrollingFromRecycler) {
                isScrollingFromHeader = true
                // 同步RecyclerView中每个item的滑动位置
                syncRecyclerViewScroll(scrollX)
                isScrollingFromHeader = false
            }
        }
    }

    private fun syncRecyclerViewScroll(scrollX: Int) {
        // 遍历RecyclerView中可见的ViewHolder
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (child is HorizontalScrollView) {
                child.scrollTo(scrollX, 0)
            }
        }
    }

    override fun onScrolled(dx: Int, dy: Int) {
        super.onScrolled(dx, dy)
        
        // 当RecyclerView滚动时，确保新出现的item与表头同步
        if (!isScrollingFromHeader) {
            headerScrollView?.let { headerScroll ->
                val scrollX = headerScroll.scrollX
                syncRecyclerViewScroll(scrollX)
            }
        }
    }

    override fun onChildAttachedToWindow(child: android.view.View) {
        super.onChildAttachedToWindow(child)
        // 当新的item添加到RecyclerView时，设置其滑动监听并同步位置
        if (child is HorizontalScrollView) {
            setupItemScrollListener(child)
            // 同步当前表头的滑动位置
            headerScrollView?.let { headerScroll ->
                child.scrollTo(headerScroll.scrollX, 0)
            }
        }
    }

    private fun setupItemScrollListener(itemScrollView: HorizontalScrollView) {
        itemScrollView.setOnScrollChangeListener { _, scrollX, _, _, _ ->
            if (!isScrollingFromHeader) {
                isScrollingFromRecycler = true
                // 同步表头滑动
                headerScrollView?.scrollTo(scrollX, 0)
                // 同步其他item的滑动
                syncRecyclerViewScroll(scrollX)
                isScrollingFromRecycler = false
            }
        }
    }
}
