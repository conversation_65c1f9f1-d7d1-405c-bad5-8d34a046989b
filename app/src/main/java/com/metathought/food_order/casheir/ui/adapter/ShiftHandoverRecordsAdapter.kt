package com.metathought.food_order.casheir.ui.adapter

import android.annotation.SuppressLint
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.data.model.base.request_model.work_handover.CashRegisterHandoverLogVo
import com.metathought.food_order.casheir.databinding.ShiftHandoverRecordsItemBinding
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigit
import com.metathought.food_order.casheir.extension.decimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.priceDecimalFormatTwoDigitZero
import com.metathought.food_order.casheir.extension.priceFormatTwoDigit
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import java.math.BigDecimal

class ShiftHandoverRecordsAdapter : RecyclerView.Adapter<ShiftHandoverRecordsAdapter.ViewHolder>() {

    private val list = ArrayList<CashRegisterHandoverLogVo>()

    var onDetailClick: ((CashRegisterHandoverLogVo) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            ShiftHandoverRecordsItemBinding.inflate(
                LayoutInflater.from(
                    parent.context
                ), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(position)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(list: List<CashRegisterHandoverLogVo>) {
        this.list.clear()
        this.list.addAll(list)
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addList(list: List<CashRegisterHandoverLogVo>) {
        this.list.addAll(list)
        notifyDataSetChanged()
    }


    inner class ViewHolder(val binding: ShiftHandoverRecordsItemBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.run {
                root.setOnClickListener {
                    bindingAdapterPosition.let {
                        val detail = list[it]
                        detail.userName
                        onDetailClick?.invoke(detail)
                    }
                }
            }
        }

        fun bind(position: Int) {
            binding.apply {
                list[position].let { log ->
                    tvStaffName.text = log.userName
                    //备用金
                    tvImprest.text =
                        root.context.getString(R.string.imprest_) + log.getOpeningCashStr()
//                        if ((log.openingCashKhr ?: 0) < 0)
//                            root.context.getString(R.string.imprest_) + "${
//                                log.openingCashUsd?.priceFormatTwoDigitZero2(
//                                    "$"
//                                )
//                            } ${log.openingCashKhr?.priceFormatTwoDigit("៛")}"
//                        else
//                            root.context.getString(R.string.imprest_) + "${
//                                log.openingCashUsd?.priceFormatTwoDigitZero2(
//                                    "$"
//                                )
//                            } + ${log.openingCashKhr?.priceFormatTwoDigit("៛")}"

                    //当班金额
                    tvShiftAmount.text =
                        root.context.getString(R.string.shift_amount_) + if (log.balance == null) "$0" else "${
                            log.balance?.priceFormatTwoDigitZero2(
                                "$"
                            )
                        }"
                    //交班金额
                    tvHandoverAmount.text =
                        root.context.getString(R.string.handover_amount_) + log.getUsdAmount()
//                                "${log.usdAmount?.priceFormatTwoDigitZero2("$")}" +
//                                " + ${log.khrAmount?.priceFormatTwoDigit("៛")}"
                    //订单金额
                    tvOrderAamount.text =
                        root.context.getString(R.string.order_amount_) + if (log.totalOrderPrice == null) "$0.00" else "${
                            log.totalOrderPrice?.priceFormatTwoDigitZero2(
                                "$"
                            )
                        }"
                    //订单笔数
                    tvOrderNum.text =
                        root.context.getString(R.string.order_num_) + if (log.totalOrderNumber == null) "0" else log.totalOrderNumber
                    tvTime.text =
                        "${log.loginTime?.formatDate()} - ${if (log.logoutTime == null) "" else log.logoutTime?.formatDate()}"

                    //相差金额
                    val startStr =
                        SpannableString("${root.context.getString(R.string.difference_amount)}: ")
                    val endStr = SpannableString(
                        if (log.discrepancyPrice.isNullOrEmpty()) "$0.00" else "${
                            log.discrepancyPrice?.toBigDecimalOrNull()?.priceFormatTwoDigitZero2(
                                "$"
                            )
                        }"
                    )

                    startStr.setSpan(
                        ForegroundColorSpan(root.context.getColor(R.color.black)),
                        0,
                        startStr.length,
                        SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    val diffenceAmount =
                        log.discrepancyPrice?.toBigDecimalOrNull() ?: BigDecimal.ZERO
                    val color = if (diffenceAmount < BigDecimal.ZERO) {
                        root.context.getColor(R.color.color_ff3141)
                    } else if (diffenceAmount > BigDecimal.ZERO) {
                        root.context.getColor(R.color.primaryColor)
                    } else {
                        root.context.getColor(R.color.black)
                    }
                    endStr.setSpan(
                        ForegroundColorSpan(
                            color
                        ),
                        0,
                        endStr.length,
                        SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    val spannableStringBuilder = SpannableStringBuilder()
                    spannableStringBuilder.append(startStr)
                    spannableStringBuilder.append(endStr)
                    tvDifferenceAmount.text = spannableStringBuilder

                }

            }
        }

    }

}