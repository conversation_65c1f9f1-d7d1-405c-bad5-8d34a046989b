package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.metathought.food_order.casheir.R
import com.metathought.food_order.casheir.constant.BalanceStatusEnum
import com.metathought.food_order.casheir.constant.BalanceTypeEnum
import com.metathought.food_order.casheir.constant.PermissionEnum
import com.metathought.food_order.casheir.data.model.base.response_model.member.rechargelist.RecordBalance
import com.metathought.food_order.casheir.databinding.BalanceListItemsBinding
import com.metathought.food_order.casheir.extension.formatDate
import com.metathought.food_order.casheir.extension.getBalanceStatusType
import com.metathought.food_order.casheir.extension.getBalanceType
import com.metathought.food_order.casheir.extension.getBalanceTypeColor
import com.metathought.food_order.casheir.extension.priceFormatTwoDigitZero2
import com.metathought.food_order.casheir.extension.setVisibleInvisible
import com.metathought.food_order.casheir.ui.app_dashbord.MainDashboardFragment


/**
 * <AUTHOR>
 * @date 2024/3/2222:18
 * @description
 */
class MemberBalanceListAdapter(
    val list: ArrayList<RecordBalance>,
    val onDetailClickListener: (RecordBalance) -> Unit,
    val onItemClickListener: (RecordBalance) -> Unit,
) : RecyclerView.Adapter<MemberBalanceListAdapter.BalanceListViewHolder>() {


    inner class BalanceListViewHolder(val binding: BalanceListItemsBinding) :
        RecyclerView.ViewHolder(binding.root) {


        fun bind(resource: RecordBalance, position: Int) {
            itemView.context.let { context ->
                resource.let {
                    binding.apply {
//                        Glide.with(context).load(it.photoUrl).circleCrop()
//                            .diskCacheStrategy(DiskCacheStrategy.ALL)
//                            .error(R.drawable.ic_logo).placeholder(R.drawable.ic_logo)
//                            .into(imgMemberProfile)
                        tvAccountName.text =
                            if (!it.nickName.isNullOrEmpty()) it.nickName else it.telephone?.takeLast(
                                4
                            )
                        tvAccountNumber.text = it.telephone
                        tvType.text = it.type?.getBalanceType(context)

                        tvPaymentMethod.text = it.getPaymentMethod(context)

                        tvStatus.text = it.status?.getBalanceStatusType(context)
                        imgStatus.setColorFilter(
                            ContextCompat.getColor(
                                context,
                                it.status?.getBalanceTypeColor() ?: R.color.cancel_color
                            )
                        )
                        tvLastTopUpDate.text = it.createTime?.formatDate()
                        tvRechargeAmount.text = it.amount?.priceFormatTwoDigitZero2()
                        btnCancel.setVisibleInvisible(
                            it.type == BalanceTypeEnum.TOP_UP.id
                                    && it.status == BalanceStatusEnum.SUCCESS.id
                                    &&  MainDashboardFragment.CURRENT_USER?.getPermissionList()
                                ?.contains(PermissionEnum.RECHARGE.type) == true
                        )
                        btnCancel.setOnClickListener {
                            onItemClickListener.invoke(resource)
                        }
                        btnDetail.setOnClickListener {
                            onDetailClickListener.invoke(resource)
                        }
                    }
                }
            }

        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BalanceListViewHolder {
        val itemView =
            BalanceListItemsBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return BalanceListViewHolder(itemView)
    }

    override fun getItemCount(): Int {
        return list.size
    }

    override fun onBindViewHolder(holder: BalanceListViewHolder, position: Int) {
        list[position].let { holder.bind(it, position) }
    }


    fun replaceData(list: ArrayList<RecordBalance>?) {
        if (list != null) {
            this.list.clear()
            this.list.addAll(list)
            notifyDataSetChanged()
        }
    }

    fun addData(newData: ArrayList<RecordBalance>?) {
        if (newData != null) {
            this.list.addAll(newData)
            notifyItemRangeInserted(this.list.size - newData.size, newData.size)
        }
    }


}