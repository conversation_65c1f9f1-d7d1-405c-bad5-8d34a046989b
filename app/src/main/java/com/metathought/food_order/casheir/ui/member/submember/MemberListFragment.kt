package com.metathought.food_order.casheir.ui.member.submember

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.viewModels
import com.google.android.material.datepicker.MaterialDatePicker
import com.metathought.food_order.casheir.MyApplication
import com.metathought.food_order.casheir.constant.FORMAT_DATE
import com.metathought.food_order.casheir.constant.FORMAT_DATE_REALIZED
import com.metathought.food_order.casheir.constant.UpdateConsumerType
import com.metathought.food_order.casheir.data.model.base.response_model.customer.CustomerMemberResponse
import com.metathought.food_order.casheir.data.model.base.response_model.member.Record
import com.metathought.food_order.casheir.databinding.FragmentMemberListBinding
import com.metathought.food_order.casheir.extension.hideKeyboard
import com.metathought.food_order.casheir.extension.toJson
import com.metathought.food_order.casheir.ui.adapter.MemberListAdapter
import com.metathought.food_order.casheir.ui.adapter.MemberOperationAdapter
import com.metathought.food_order.casheir.ui.common.BaseFragment
import com.metathought.food_order.casheir.ui.member.MemberMainFragment
import com.metathought.food_order.casheir.ui.member.MemberMainViewModel
import com.metathought.food_order.casheir.ui.member.balace.MemberBalanceFragment
import com.metathought.food_order.casheir.ui.member.balace.topup.TopUpSuccessDialog
import com.metathought.food_order.casheir.ui.member.balace.topup.TopupBalanceDialog
import com.metathought.food_order.casheir.ui.member.dialog.CreateMemberInfoDialog
import com.metathought.food_order.casheir.ui.member.dialog.EditMemberInfoDialog
import com.metathought.food_order.casheir.ui.member.dialog.EditMemberInfoTipsDialog
import com.metathought.food_order.casheir.ui.member.dialog.UserCreditDetailDialog
import com.metathought.food_order.casheir.ui.second_display.SecondaryScreenUI
import com.metathought.food_order.casheir.ui.widget.RecyclerViewSyncManager
import com.metathought.food_order.casheir.ui.widget.SyncScrollRecyclerView
import com.metathought.food_order.casheir.utils.SingleClickUtils
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * @description 会员列表
 * ✅ 左右滑动正常 - 可以通过表头或列表项进行滑动
 * ✅ 无阻尼感 - 滑动时没有弹性回弹效果
 * ✅ 同步滑动 - 表头和所有列表项完美同步
 * ✅ 操作栏固定 - 右侧操作栏不受滑动影响
 * ✅ 上拉加载同步 - 操作列会跟着一起动
 *
 * 流畅滑动 - 没有阻尼感和淡化效果
 * 精确同步 - 表头和列表项实时同步
 * 整体滑动 - 虽然技术上每行都有HorizontalScrollView，但它们同步得非常好，给用户的感觉就像整个表格在滑动
 * 无视觉干扰 - 移除了所有可能影响滑动体验的视觉效果
 */
@AndroidEntryPoint
class MemberListFragment : BaseFragment() {

    companion object {
        fun newInstance() = MemberListFragment()
    }

    private var orderedScreen: SecondaryScreenUI? = null

    private var _binding: FragmentMemberListBinding? = null
    private val memberMainViewModel: MemberMainViewModel by viewModels()
    private val binding get() = _binding
    private lateinit var memberListOrderAdapter: MemberListAdapter
    private lateinit var memberOperationAdapter: MemberOperationAdapter
    private val syncManager = RecyclerViewSyncManager()
    var startDateString: String = ""
    var endDateString: String = ""
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentMemberListBinding.inflate(layoutInflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initSecondary()
        initView()
        initListener()
        initObserver()
    }

    private fun initSecondary() {
        context?.let {
            orderedScreen = MyApplication.myAppInstance.orderedScreen
//            getOrderedScreen()?.showOrderedInfo()
        }
    }

    private fun getOrderedScreen(): SecondaryScreenUI? {
        return orderedScreen
    }

    private fun initObserver() {
        memberMainViewModel.uiListMemberState.observe(viewLifecycleOwner) {
            binding?.apply {
                it.showLoading?.let {
                    if (it)
                        showProgress()
                }
                if (it.showEnd) {
                    dismissProgress()
                    if (it.isRefresh != false) {
                        layoutEmpty.root.isVisible = true
                        memberListOrderAdapter.replaceData(arrayListOf())
                        memberOperationAdapter.replaceData(arrayListOf())
                    } else {
                        refreshLayout.finishLoadMoreWithNoMoreData()
                    }
                }
                it.showError?.let { error ->
                    dismissProgress()
                    showToast(error)
                }

                it.showSuccess?.let { response ->
                    dismissProgress()
                    layoutEmpty.root.isVisible = false
                    if (it.isRefresh != false) {
                        ArrayList(response.records).let { it1 ->
                            memberListOrderAdapter.replaceData(it1)
                            memberOperationAdapter.replaceData(it1)
                        }
                        refreshLayout.finishRefresh()

                    } else {
                        ArrayList(response.records).let { it1 ->
                            memberListOrderAdapter.addData(it1)
                            memberOperationAdapter.addData(it1)
                        }
                        refreshLayout.finishLoadMore()
                    }
                }
            }
        }

    }

    private fun initListener() {
        binding?.apply {
            refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
                override fun onRefresh(refreshLayout: RefreshLayout) {
                    getMemberList(true)
                }

                override fun onLoadMore(refreshLayout: RefreshLayout) {
                    getMemberList(false)
                }

            })
            tvCalendar.setOnClickListener {
                datePickerDialog()
            }
            edtSearch.setTextChangedListenerCallBack {
                getMemberList()
            }
//            edtSearch.addTextChangedListener {
//                getMemberList()
//            }
//            edtSearch.setOnEditorActionListener { textView, i, keyEvent ->
//                if (i == EditorInfo.IME_ACTION_SEARCH) {
//                    context.hideKeyboard(edtSearch)
//                    true
//                }
//                false
//            }
            tvClearFilter.setOnClickListener {
                tvCalendar.text = ""
                tvCalendar.updateCalendarColor()
                startDateString = ""
                endDateString = ""
                edtSearch.setSearchContent("")
                edtSearch.removeFocus()
//                activity?.hideKeyboard(edtSearch)

            }

            tvAddMember.setOnClickListener {
                CreateMemberInfoDialog.showDialog(parentFragmentManager) {
                    tvClearFilter.performClick()
                }
            }
        }
    }

    private fun initView() {
        binding?.apply {
            context?.let {
                showProgress()
                getMemberList()
            }

            memberListOrderAdapter = MemberListAdapter(arrayListOf())
            memberOperationAdapter = MemberOperationAdapter(arrayListOf())

            memberListOrderAdapter.setOnMemberListAdapterListener(object :
                MemberListAdapter.OnMemberListAdapterListener {
                override fun onEditClick(record: Record) {
                    //编辑弹窗
                    EditMemberInfoTipsDialog.showDialog(parentFragmentManager, record) { type ->
                        EditMemberInfoDialog.showDialog(
                            parentFragmentManager,
                            type,
                            record
                        ) {
                            getMemberList()
                            when (type) {
                                UpdateConsumerType.NAME -> {

                                }

                                UpdateConsumerType.PHONE -> {

                                }
                            }
                        }
                    }
                }

                override fun onRechargeClick(record: Record) {
                    TopupBalanceDialog.showDialog(
                        parentFragmentManager,
                        menuOrderScreen = getOrderedScreen(),
                        content = CustomerMemberResponse(
                            record.consumerPayAccountId,
                            record.balance,
                            record.nickName,
                            record.paymentMethod,
                            record.telephone
                        ).toJson()
                    ) { response ->
                        if (response) {
                            TopUpSuccessDialog.showDialog(parentFragmentManager) {
                                getOrderedScreen()?.showDefault()
                            }
                            getMemberList()
                        } else {
                            Timber.e("Recharge failed")
                            getOrderedScreen()?.showDefault()
//                            getMemberList()
                        }
//                        if (response.data != null) {
//                            TopupQrDialog.showDialog(
//                                fragmentManager = activity?.supportFragmentManager
//                                    ?: parentFragmentManager,
//                                paymentResponse = response.data,
//                                getOrderedScreen(),
//                                onCloseListener = {
//                                    getOrderedScreen()?.showDefault()
//                                    getMemberList()
//                                },
//                                successListener = {
//                                    TopUpSuccessDialog.showDialog(parentFragmentManager) {
//                                        getOrderedScreen()?.showDefault()
//                                    }
//                                    getMemberList()
//                                })
//                        } else {
//                            TopUpSuccessDialog.showDialog(parentFragmentManager)
//                            getMemberList()
//                        }
                    }
                }

                override fun onDetailClick(record: Record) {
                    SingleClickUtils.isFastDoubleClick {
                        UserCreditDetailDialog.showDialog(parentFragmentManager, record.id)
//                        if (parentFragment is MemberMainFragment) {
//                            val bundle = Bundle()
//                            bundle.putString(
//                                MemberBalanceFragment.MEMBER_BALANCE_FRAGMENT_BUNDLE_DATA_PHONE,
//                                record.telephone
//                            )
//
//                            (parentFragment as MemberMainFragment).replaceBalanceFragment(
//                                bundle
//                            )
//                        }
                    }
                }

            })

            // 设置操作按钮适配器
            memberOperationAdapter.setOnOperationListener(object :
                MemberOperationAdapter.OnOperationListener {
                override fun onRechargeClick(record: Record) {
                    TopupBalanceDialog.showDialog(
                        parentFragmentManager,
                        menuOrderScreen = getOrderedScreen(),
                        content = CustomerMemberResponse(
                            record.consumerPayAccountId,
                            record.balance,
                            record.nickName,
                            record.paymentMethod,
                            record.telephone
                        ).toJson()
                    ) { response ->
                        if (response) {
                            TopUpSuccessDialog.showDialog(parentFragmentManager) {
                                getOrderedScreen()?.showDefault()
                            }
                            getMemberList()
                        } else {
                            Timber.e("Recharge failed")
                            getOrderedScreen()?.showDefault()
                        }
                    }
                }

                override fun onDetailClick(record: Record) {
                    SingleClickUtils.isFastDoubleClick {
                        UserCreditDetailDialog.showDialog(parentFragmentManager, record.id)
                    }
                }

                override fun onEditClick(record: Record) {
                    EditMemberInfoTipsDialog.showDialog(parentFragmentManager, record) { type ->
                        EditMemberInfoDialog.showDialog(
                            parentFragmentManager,
                            type,
                            record
                        ) {
                            getMemberList()
                            when (type) {
                                UpdateConsumerType.NAME -> {

                                }

                                UpdateConsumerType.PHONE -> {

                                }
                            }
                        }
                    }
                }
            })

            recyclerviewMember.adapter = memberListOrderAdapter
            recyclerviewOperations.adapter = memberOperationAdapter

            // 设置同步滚动
            syncManager.setupSync(recyclerviewMember, recyclerviewOperations)

            // 设置表头与列表项的水平同步滑动
            (recyclerviewMember as? SyncScrollRecyclerView)?.setHeaderScrollView(mainScrollView)

            // 优化滑动体验，取消阻尼感
            mainScrollView.apply {
                overScrollMode = android.view.View.OVER_SCROLL_NEVER
                isHorizontalFadingEdgeEnabled = false
                isVerticalFadingEdgeEnabled = false
            }
        }
    }

    private fun datePickerDialog() {
        // Creating a MaterialDatePicker builder for selecting a date range
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        // Building the date picker dialog
        val datePicker = builder.build()
        datePicker.addOnPositiveButtonClickListener { selection ->
            // Retrieving the selected start and end dates
            val startDate = selection.first
            val endDate = selection.second
            // Formatting the selected dates as strings
            val sdf = SimpleDateFormat(FORMAT_DATE_REALIZED, Locale.US)
            startDateString = sdf.format(Date(startDate))
            endDateString = sdf.format(Date(endDate))
            // Creating the date range string
            val showSdf = SimpleDateFormat(FORMAT_DATE, Locale.US)
            // Creating the date range string
//            val selectedDateRange = "$startDateString - $endDateString"

            // Displaying the selected date range in the TextView
            binding?.tvCalendar?.text =
                "${showSdf.format(Date(startDate))} - ${showSdf.format(Date(endDate))}"
            binding?.tvCalendar?.updateCalendarColor()
            getMemberList()
        }
        // Showing the date picker dialog
        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun getMemberList(isRefresh: Boolean? = null) {
        memberMainViewModel.getMemberList(
            isRefresh,
            keyword = binding?.edtSearch?.getSearchContent(),
            startDateString,
            endDateString
        )
    }

}