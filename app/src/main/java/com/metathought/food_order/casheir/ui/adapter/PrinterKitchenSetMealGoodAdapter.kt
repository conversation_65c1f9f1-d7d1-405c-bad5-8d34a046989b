package com.metathought.food_order.casheir.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.metathought.food_order.casheir.data.model.base.response_model.ordered.OrderMealSetGood
import com.metathought.food_order.casheir.data.model.base.response_model.print_tamplate.PrintTamplateResponseItem
import com.metathought.food_order.casheir.databinding.ItemPrinterKitchenMealSetGoodBinding
import timber.log.Timber
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2025/01/10 17:30
 * @description  厨打小票按80 样式打
 */
class PrinterKitchenSetMealGoodAdapter(
    val list: List<OrderMealSetGood>,
    val templateItem: PrintTamplateResponseItem
) : RecyclerView.Adapter<PrinterKitchenSetMealGoodAdapter.PrinterFeedViewHolder>() {

    inner class PrinterFeedViewHolder(val binding: ItemPrinterKitchenMealSetGoodBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(resource: OrderMealSetGood) {
            binding.run {
                val localeList = mutableListOf<Locale>()
                val langList = templateItem.getLangList()
                langList.forEach {
                    localeList.add(Locale(it.uppercase()))
                }
                if (localeList.isEmpty()) {
                    localeList.add(Locale("EN"))
                }
                val numStr = if ((resource.num ?: 0) > 1) " *${resource.num}" else ""
                val weight = if(resource.isToBeWeighed() && resource.isHasCompleteWeight()) "(${resource.getWeightStr()})" else ""
                localeList.forEachIndexed { index, locale ->
                    val name = resource.getNameByLocale(locale)
                    if (index == 0) {
                        tvFeedNameEn.text = "—${name}${numStr}${weight}"
                        tvFeedNameEn.isVisible = !name.isNullOrEmpty()
                    } else if (index == 1) {
                        tvFeedNameKm.text = "—${name}${numStr}${weight}"
                        tvFeedNameKm.isGone =
                            tvFeedNameKm.text == tvFeedNameEn.text || name.isNullOrEmpty()
                    }
                }

                Timber.e("tvFeedName.text-> ${tvFeedNameEn.text}  tvFeedNameKm.text->${tvFeedNameKm.text} ")
                tvFeedFoodCount.text = "x${resource.number}"

                rvMealGoodTag.adapter =
                    PrinterKitchenSetMealGoodTagAdapter(
                        resource.mealSetTagItemList ?: listOf(),
                        resource.mealSetTagItemListEn ?: listOf(),
                        resource.mealSetTagItemListKm ?: listOf(),
                        templateItem
                    )
                if (templateItem.informationShow != null) {
                    tvFeedNameEn.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context)
                    tvFeedNameKm.textSize =
                        templateItem.informationShow.getProductNameFontRealSize(itemView.context)
                    tvFeedFoodCount.textSize =
                        templateItem.informationShow.getProductNumFontRealSize(itemView.context)
                }
            }
        }


    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PrinterFeedViewHolder(
        ItemPrinterKitchenMealSetGoodBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
    )

    override fun getItemCount() = list.size

    override fun onBindViewHolder(
        holder: PrinterKitchenSetMealGoodAdapter.PrinterFeedViewHolder,
        position: Int
    ) {
        holder.bind(list[position])
    }

}